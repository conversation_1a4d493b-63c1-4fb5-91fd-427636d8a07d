{
    "version": "0.2.0",
    "configurations": [
        {
            "name": "Current Go File",
            "type": "go",
            "request": "launch",
            "mode": "auto",
            "program": "${file}",
            "cwd": "/home/<USER>/Desktop/AriaDirect/aria-backend/python_services/test"
        },
        {
            "name": "Node.js: Current File",
            "type": "node",
            "request": "launch",
            "program": "${file}",
            "console": "integratedTerminal",
            "skipFiles": [
                "<node_internals>/**"
            ],
            "cwd": "${fileDirname}"
        },
        {
            "name": "[Stag] App Version",
            "type": "go",
            "request": "launch",
            "mode": "auto",
            "program": "${workspaceRoot}/golang_services/app_version",
            "args": [
                "svc",
                "load",
                "123.yml"
            ]
        },
        {
            "name": "[Stag] Zalo + WhatsApp",
            "type": "go",
            "request": "launch",
            "mode": "auto",
            "program": "${workspaceRoot}/golang_services/zalo-mini-app-service",
            "cwd": "${workspaceRoot}/golang_services/zalo-mini-app-service",
            "args": [
                "start",
            ]
        },
        {
            "name": "[Stag] Email WebHook",
            "type": "go",
            "request": "launch",
            "mode": "auto",
            "program": "${workspaceRoot}/golang_services/email-hook",
            "args": [
                "svc",
            ],
            "envFile": "${workspaceRoot}/.vscode/.env_stag"
        },
        {
            "name": "[Stag] Atlas",
            "type": "go",
            "request": "launch",
            "mode": "auto",
            "program": "${workspaceRoot}/golang_services/atlas",
            "args": [
                "--account-service-config",
                "ad_user_account_service",
                "--ad_secrets",
                "ad_secrets",
                "--callback-host",
                "aria-backend.leoit.xyz"
            ],
            "envFile": "${workspaceRoot}/.vscode/.env_stag"
        },
        {
            "name": "[Stag] Browser Service",
            "program": "${workspaceFolder}/nodejs_services/browser_service/app.js",
            "request": "launch",
            "skipFiles": [
                "<node_internals>/**"
            ],
            "type": "node",
            "cwd": "${workspaceFolder}/nodejs_services/browser_service",
            "env": {
                "FORM_PORT": "8003"
            },
            "envFile": "${workspaceRoot}/.vscode/.env_stag"
        },
        {
            "name": "[Stag] Carrier",
            "type": "go",
            "request": "launch",
            "mode": "auto",
            "program": "${workspaceRoot}/golang_services/carrier",
            "args": [
                "s",
                "--ad_sqs",
                "ad_sqs",
                "--ad_aws",
                "ad_aws",
                "--ad_s3",
                "ad_s3",
                "--ad_fedex",
                "ad_fedex",
                "--ad_api_token",
                "ad_api_token"
            ],
            "envFile": "${workspaceRoot}/.vscode/.env_stag"
        },
        {
            "name": "[Stag] Chatbot",
            "type": "go",
            "request": "launch",
            "mode": "auto",
            "program": "${workspaceRoot}/golang_services/chatbot",
            "args": [
                "svc",
                "--db-config",
                "ad_db",
                "--ad_secrets",
                "ad_secrets",
            ],
            "envFile": "${workspaceRoot}/.vscode/.env_stag"
        },
        {
            "name": "[Stag] Command Script",
            "type": "go",
            "request": "launch",
            "mode": "auto",
            "program": "${workspaceRoot}/golang_services/command_script",
            "args": [
                "svc"
            ],
            "envFile": "${workspaceRoot}/.vscode/.env_stag"
        },
        {
            "name": "[Stag] Device Service",
            "type": "go",
            "request": "launch",
            "mode": "auto",
            "program": "${workspaceRoot}/golang_services/devices",
            "args": [
                "svc",
                "--ad_firebase",
                "ad_firebase",
                "--db-config",
                "ad_db",
                "--ad_secrets",
                "ad_secrets",
                "--account-service-config",
                "ad_user_account_service",
                "--ad_api_token",
                "ad_api_token"
            ],
            "envFile": "${workspaceRoot}/.vscode/.env_stag"
        },
        {
            "name": "[Stag] Example",
            "type": "go",
            "request": "launch",
            "mode": "debug",
            "program": "${workspaceRoot}/golang_services/examples"
        },
        {
            "name": "[Stag] FexEX Carrier",
            "type": "go",
            "request": "launch",
            "mode": "auto",
            "program": "${workspaceRoot}/golang_services/carrier",
            "args": [
                "s",
                "--ad_sqs",
                "ad_sqs",
                "--ad_aws",
                "ad_aws",
                "--ad_s3",
                "ad_s3",
                "--ad_fedex",
                "ad_fedex",
                "--ad_api_token",
                "ad_api_token"
            ],
            "envFile": "${workspaceRoot}/.vscode/.env_stag"
        },
        {
            "name": "[Stag] FILL PDF FORM",
            "type": "go",
            "request": "launch",
            "mode": "auto",
            "program": "${workspaceRoot}/golang_services/fill-pdf-form",
            "args": [
                "s",
                "--ad_sqs",
                "ad_sqs",
                "--ad_aws",
                "ad_aws",
                "--ad_s3",
                "ad_s3",
                "--ad_fedex",
                "ad_fedex",
                "--ad_api_token",
                "ad_api_token"
            ],
            "envFile": "${workspaceRoot}/.vscode/.env_stag"
        },
        {
            "type": "node",
            "request": "launch",
            "name": "[Stag] Google Sheet service",
            "runtimeExecutable": "node",
            "runtimeArgs": [
                "app.js"
            ],
            "cwd": "${workspaceFolder}/nodejs_services/google_sheet_service",
            "envFile": "${workspaceRoot}/.vscode/.env_stag"
        },
        {
            "type": "node",
            "request": "launch",
            "name": "[Prod] Google Sheet service",
            "runtimeExecutable": "node",
            "runtimeArgs": [
                "app.js"
            ],
            "cwd": "${workspaceFolder}/nodejs_services/google_sheet_service",
            "envFile": "${workspaceRoot}/.vscode/.env_prod"
        },
        {
            "type": "node",
            "request": "launch",
            "name": "[Stag] Helper service",
            "runtimeExecutable": "node",
            "runtimeArgs": [
                "index.js"
            ],
            "cwd": "${workspaceFolder}/nodejs_services/helper_service",
            "envFile": "${workspaceRoot}/.vscode/.env_stag"
        },
        {
            "name": "[Stag] Launch Node.js in Docker",
            "type": "docker",
            "request": "launch",
            "preLaunchTask": "Run Docker Container",
            "platform": "node",
            "image": "node:latest",
            "envFile": "${workspaceRoot}/.vscode/.env_stag"
        },
        {
            "name": "[Stag] MRZ Passport Parser",
            "type": "go",
            "request": "launch",
            "mode": "auto",
            "program": "${workspaceRoot}/golang_services/mrz_parser",
            "buildFlags": "--buildvcs=false",
            "args": [
                "--db-config",
                "ad_db"
            ],
            "envFile": "${workspaceRoot}/.vscode/.env_stag"
        },
        {
            "name": "[Stag] Node.js: Current File",
            "type": "node",
            "request": "launch",
            "program": "${file}",
            "console": "integratedTerminal",
            "cwd": "${workspaceFolder}"
        },
        {
            "type": "node",
            "request": "launch",
            "name": "[Stag] Nodejs Local",
            "runtimeExecutable": "nodemon",
            "args": [],
            "runtimeArgs": [
                "--exec",
                "node examples/send-sqs-queue/app.js"
            ]
        },
        {
            "name": "[Stag] Notification",
            "type": "go",
            "request": "launch",
            "mode": "auto",
            "program": "${workspaceRoot}/golang_services/notification",
            "args": [
                "n",
                "--ad_sqs",
                "ad_sqs",
                "--ad_aws",
                "ad_aws",
                "--ad_db",
                "ad_db",
                "--ad_s3",
                "ad_s3",
                "--ad_ses",
                "ad_ses"
            ],
            "envFile": "${workspaceRoot}/.vscode/.env_stag"
        },
        {
            "name": "[Stag] Notification User",
            "type": "go",
            "request": "launch",
            "mode": "auto",
            "program": "${workspaceRoot}/golang_services/notification-user",
            "args": [
                "n",
                "--ad_sqs",
                "ad_sqs",
                "--ad_aws",
                "ad_aws",
                "--ad_email",
                "ad_email",
                "--ad_website",
                "ad_website",
                "--ad_api_token",
                "ad_api_token",
                "--localize-file",
                "../conf/localize.yaml"
            ],
            "envFile": "${workspaceRoot}/.vscode/.env_stag"
        },
        {
            "name": "[Stag] Packages",
            "type": "go",
            "request": "launch",
            "mode": "auto",
            "program": "${workspaceRoot}/golang_services/packages",
            "buildFlags": "--buildvcs=false",
            "args": [
                "svc",
                "--db-config",
                "db-config",
                "--s3-bucket-config",
                "s3-bucket-config",
                "--sqs-config",
                "sqs-config",
                "--conf-file",
                "../conf/packages.yaml",
                "--localize-file",
                "../conf/localize.yaml",
                "--client-secret",
                "aaaa",
                "--ad_secrets",
                "ad_secrets_1",
                "--internal-auth-service-host",
                "bbbb",
                "--service-config",
                "packages_svc_config",
                "--account-service-config",
                "account-service-config",
                "--ad_packer_service",
                "ad_packer_service",
                "--ad_endpoint",
                "ad_endpoint"
            ],
            "envFile": "${workspaceRoot}/.vscode/.env_stag",
            "cwd": "${workspaceRoot}/golang_services/packages"
        },
        {
            "type": "node",
            "request": "launch",
            "name": "[Stag] Packer Offline",
            "runtimeExecutable": "nodemon",
            "runtimeArgs": [
                "--exec",
                "node src/index.js"
            ],
            "cwd": "${workspaceFolder}/nodejs_services/packer_offline",
            "envFile": "${workspaceRoot}/.vscode/.env_stag"
        },
        {
            "type": "node",
            "request": "launch",
            "name": "[Stag] Packer Online",
            "runtimeExecutable": "nodemon",
            "runtimeArgs": [
                "--exec",
                "node src/index.js"
            ],
            "cwd": "${workspaceFolder}/nodejs_services/packer_online",
            "envFile": "${workspaceRoot}/.vscode/.env_stag"
        },
        {
            "name": "[Stag] Packer Service",
            "type": "go",
            "request": "launch",
            "mode": "auto",
            "program": "${workspaceRoot}/golang_services/packer",
            "args": [
                "p",
                "--ad_db",
                "ad_db",
                "--ad_aws",
                "ad_aws",
                "--ad_sqs",
                "ad_sqs",
                "--ad_s3",
                "ad_s3",
                "--ad_packer_service",
                "ad_packer_service",
                "--ad_user_account_service",
                "ad_user_account_service",
                "--ad_secrets",
                "ad_secrets",
                "--ad_api_token",
                "ad_api_token"
            ],
            "envFile": "${workspaceRoot}/.vscode/.env_stag"
        },
        {
            "type": "node",
            "request": "launch",
            "name": "[Stag] Passport photo",
            "runtimeExecutable": "nodemon",
            "runtimeArgs": [
                "--exec",
                "node src/index.js"
            ],
            "cwd": "${workspaceRoot}/nodejs_services/passport_photo",
            "envFile": "${workspaceRoot}/.vscode/.env_stag"
        },
        {
            "name": "[Stag] Payment",
            "type": "go",
            "request": "launch",
            "mode": "auto",
            "program": "${workspaceRoot}/golang_services/payments",
            "args": [
                "svc",
                "--db-config",
                "ad_db",
                "--use-wechat-pay-prod",
                "--conf-file",
                "../conf/payments.yaml",
                "--service-config",
                "ad_payment_service",
                "--sqs-config",
                "sqs-config",
                "--account-service-config",
                "ad_user_account_service",
                "--ad_secrets",
                "ad_secrets",
                "--ad_website",
                "ad_website",
                "--ad_authorize",
                "ad_authorize",
                "--ad_wechatpay",
                "ad_wechatpay",
                "--ad_onepay",
                "ad_onepay",
                "--ad_paypal",
                "ad_paypal",
                "--ad_zellepay",
                "ad_zellepay",
                "--ad_stripe",
                "ad_stripe",
                "--version-file-bucket",
                "ad-app-version",
                "--region-payment-config-file",
                "ad_config_file"
            ],
            "envFile": "${workspaceRoot}/.vscode/.env_stag"
        },
        {
            "name": "[Stag] Payment worker",
            "type": "go",
            "request": "launch",
            "mode": "auto",
            "program": "${workspaceRoot}/golang_services/payment_worker",
            "preLaunchTask": "get_env",
            "args": [
                "svc",
                "--db-config",
                "ad_db",
                "--use-wechat-pay-prod",
                "--use-authorize-prod",
                "--conf-file",
                "../conf/payments.yaml",
                "--ad_authorize",
                "ad_authorize",
                "--ad_wechatpay",
                "ad_wechatpay",
                "--ad_onepay",
                "ad_onepay"
            ],
            "envFile": "${workspaceRoot}/.vscode/.env_stag"
        },
        {
            "name": "[Stag] Python: Current File",
            "type": "python",
            "request": "launch",
            "program": "${file}",
            "console": "integratedTerminal",
            "justMyCode": true
        },
        {
            "name": "[Stag] Search",
            "type": "go",
            "request": "launch",
            "mode": "auto",
            "program": "${workspaceRoot}/golang_services/search",
            "args": [
                "s",
                "--ad_es",
                "ad_es",
                "--ad_aws",
                "ad_aws",
                "--ad_user_account_service",
                "ad_user_account_service",
                "--ad_secrets",
                "ad_secrets"
            ],
            "envFile": "${workspaceRoot}/.vscode/.env_stag"
        },
        {
            "name": "[Stag] Tests",
            "type": "go",
            "request": "launch",
            "mode": "test",
            "program": "${fileDirname}",
            "env": {},
            "args": [
                "^TestGenerateConfigurationFailure$"
            ]
        },
        {
            "name": "[Stag] Tests",
            "type": "go",
            "request": "launch",
            "mode": "test",
            "remotePath": "",
            "program": "${fileDirname}",
            "env": {},
            "args": [
                "^TestGenerateConfigurationFailure$"
            ],
            "envFile": "${workspaceRoot}/.vscode/.env_stag"
        },
        {
            "name": "[Stag] Travel Photo",
            "type": "go",
            "request": "launch",
            "mode": "auto",
            "program": "${workspaceRoot}/golang_services/travel-photo",
            "args": [
                "--db-config",
                "db-config"
            ],
            "envFile": "${workspaceRoot}/.vscode/.env_stag"
        },
        {
            "name": "[Stag] User authorization",
            "type": "go",
            "request": "launch",
            "mode": "auto",
            "program": "${workspaceRoot}/golang_services/user-authentication",
            "args": [
                "svc",
                "--use-wechat-pay-prod",
                "--conf-file",
                "--ad_sqs",
                "ad_sqs",
                "--ad_s3",
                "ad_s3",
                "--ad_user_account_service",
                "account-service-config"
            ],
            "envFile": "${workspaceRoot}/.vscode/.env_stag"
        },
        {
            "name": "[Stag] Watchdog",
            "type": "go",
            "request": "launch",
            "mode": "auto",
            "program": "${workspaceRoot}/golang_services/watchdog",
            "args": [
                "svc",
                "--service-config",
                "ad_watchdog_service",
                "--sqs-config",
                "ad_sqs",
                "--ad_secrets",
                "ad_secrets",
                "--ad_fedex",
                "ad_fedex",
                "-ad-s3",
                "ad_s3"
            ],
            "envFile": "${workspaceRoot}/.vscode/.env_stag"
        },
        {
            "name": "[Stag] Zalo Chat",
            "type": "go",
            "request": "launch",
            "mode": "auto",
            "program": "${workspaceRoot}/golang_services/zalo-mini-app-service",
            "args": [
                "start"
            ],
            "envFile": "${workspaceRoot}/.vscode/.env_stag"
        },
        {
            "name": "[Stag] album_photo",
            "type": "go",
            "request": "launch",
            "mode": "auto",
            "program": "${workspaceRoot}/golang_services/album_photo",
            "args": [
                "svc",
                "--db-config",
                "db-config",
                "--ad_aws",
                "ad_aws",
                "--ad_secrets",
                "ad_secrets",
                "--account-service-config",
                "account-service-config",
                "--s3-bucket-config",
                "s3-bucket-config"
            ],
            "envFile": "${workspaceRoot}/.vscode/.env_stag"
        },
        {
            "name": "[Stag] master_data",
            "type": "go",
            "request": "launch",
            "mode": "auto",
            "program": "${workspaceRoot}/golang_services/master_data",
            "args": [
                "svc",
                "--db-config",
                "db-config",
                "--ad_secrets",
                "ad_secrets",
                "--account-service-config",
                "account-service-config"
            ],
            "envFile": "${workspaceRoot}/.vscode/.env_stag"
        },
        {
            "name": "[Stag] shipments",
            "type": "go",
            "request": "launch",
            "mode": "auto",
            "program": "${workspaceRoot}/golang_services/shipments",
            "args": [
                "service",
                "--db-config",
                "ad_db"
            ],
            "envFile": "${workspaceRoot}/.vscode/.env_stag"
        },
        {
            "name": "[Stag] submit-dispatcher",
            "type": "go",
            "request": "launch",
            "mode": "auto",
            "program": "${workspaceRoot}/golang_services/submit-dispatcher",
            "args": [
                "s",
                "--ad_db",
                "ad_db",
                "--ad_sqs",
                "ad_sqs",
                "--ad_aws",
                "ad_aws",
                "--ad_package_service",
                "ad_package_service",
                "--ad_s3",
                "ad_s3",
                "--ad_email",
                "ad_email",
                "--ad_website",
                "ad_website",
                "--localize-file",
                "../conf/localize.yaml"
            ],
            "envFile": "${workspaceRoot}/.vscode/.env_stag"
        },
        {
            "name": "[Stag] submit-email-worker",
            "type": "go",
            "request": "launch",
            "mode": "auto",
            "program": "${workspaceRoot}/golang_services/submit-email-worker",
            "args": [
                "s",
                "--ad_sqs",
                "ad_sqs",
                "--ad_aws",
                "ad_aws",
                "--ad_s3",
                "ad_s3",
                "--ad_submit_services",
                "ad_submit_services",
                "--localize-file",
                "../conf/localize.yaml",
                "--ad_db",
                "ad_db",
                "--ad_ses",
                "ad_ses",
                "--ad_email",
                "ad_email",
                "--ad_website",
                "ad_website",
                "--ad_api_token",
                "ad_api_token"
            ],
            "envFile": "${workspaceRoot}/.vscode/.env_stag"
        },
        {
            "type": "node",
            "request": "launch",
            "name": "[Stag] third_party_service",
            "runtimeExecutable": "node",
            "runtimeArgs": [
                "index.js"
            ],
            "cwd": "${workspaceFolder}/nodejs_services/third_party_service",
            "envFile": "${workspaceRoot}/.vscode/.env_stag"
        },
        {
            "name": "[Prod] App Version",
            "type": "go",
            "request": "launch",
            "mode": "auto",
            "program": "${workspaceRoot}/golang_services/app_version",
            "args": [
                "svc",
                "load",
                "123.yml"
            ]
        },
        {
            "name": "[Prod] Atlas",
            "type": "go",
            "request": "launch",
            "mode": "auto",
            "program": "${workspaceRoot}/golang_services/atlas",
            "args": [
                "--account-service-config",
                "ad_user_account_service",
                "--ad_secrets",
                "ad_secrets",
                "--callback-host",
                "aria-backend.leoit.xyz"
            ],
            "envFile": "${workspaceRoot}/.vscode/.env_prod"
        },
        {
            "name": "[Prod] Browser Service",
            "program": "${workspaceFolder}/nodejs_services/browser_service/app.js",
            "request": "launch",
            "skipFiles": [
                "<node_internals>/**"
            ],
            "type": "node",
            "cwd": "${workspaceFolder}/nodejs_services/browser_service",
            "env": {
                "FORM_PORT": "8003"
            },
            "envFile": "${workspaceRoot}/.vscode/.env_prod"
        },
        {
            "name": "[Prod] Carrier",
            "type": "go",
            "request": "launch",
            "mode": "auto",
            "program": "${workspaceRoot}/golang_services/carrier",
            "args": [
                "s",
                "--ad_sqs",
                "ad_sqs",
                "--ad_aws",
                "ad_aws",
                "--ad_s3",
                "ad_s3",
                "--ad_fedex",
                "ad_fedex",
                "--ad_api_token",
                "ad_api_token"
            ],
            "envFile": "${workspaceRoot}/.vscode/.env_prod"
        },
        {
            "name": "[Prod] Chatbot",
            "type": "go",
            "request": "launch",
            "mode": "auto",
            "program": "${workspaceRoot}/golang_services/chatbot",
            "args": [
                "svc",
                "--db-config",
                "ad_db"
            ],
            "envFile": "${workspaceRoot}/.vscode/.env_prod"
        },
        {
            "name": "[Prod] Command Script",
            "type": "go",
            "request": "launch",
            "mode": "auto",
            "program": "${workspaceRoot}/golang_services/command_script",
            "args": [
                "svc"
            ],
            "envFile": "${workspaceRoot}/.vscode/.env_prod"
        },
        {
            "name": "[Prod] Device Service",
            "type": "go",
            "request": "launch",
            "mode": "auto",
            "program": "${workspaceRoot}/golang_services/devices",
            "args": [
                "svc",
                "--ad_firebase",
                "ad_firebase",
                "--db-config",
                "ad_db",
                "--ad_secrets",
                "ad_secrets",
                "--account-service-config",
                "ad_user_account_service",
                "--ad_api_token",
                "ad_api_token"
            ],
            "envFile": "${workspaceRoot}/.vscode/.env_prod"
        },
        {
            "name": "[Prod] Email WebHook",
            "type": "go",
            "request": "launch",
            "mode": "auto",
            "program": "${workspaceRoot}/golang_services/email-hook",
            "args": [
                "svc",
            ],
            "envFile": "${workspaceRoot}/.vscode/.env_prod"
        },
        {
            "name": "[Prod] Example",
            "type": "go",
            "request": "launch",
            "mode": "debug",
            "program": "${workspaceRoot}/golang_services/examples"
        },
        {
            "name": "[Prod] FexEX Carrier",
            "type": "go",
            "request": "launch",
            "mode": "auto",
            "program": "${workspaceRoot}/golang_services/carrier",
            "args": [
                "s",
                "--ad_sqs",
                "ad_sqs",
                "--ad_aws",
                "ad_aws",
                "--ad_s3",
                "ad_s3",
                "--ad_fedex",
                "ad_fedex",
                "--ad_api_token",
                "ad_api_token"
            ],
            "envFile": "${workspaceRoot}/.vscode/.env_prod"
        },
        {
            "name": "[Prod] FILL PDF FORM",
            "type": "go",
            "request": "launch",
            "mode": "auto",
            "program": "${workspaceRoot}/golang_services/fill-pdf-form",
            "args": [
                "s",
                "--ad_sqs",
                "ad_sqs",
                "--ad_aws",
                "ad_aws",
                "--ad_s3",
                "ad_s3",
                "--ad_fedex",
                "ad_fedex",
                "--ad_api_token",
                "ad_api_token"
            ],
            "envFile": "${workspaceRoot}/.vscode/.env_prod"
        },
        {
            "type": "node",
            "request": "launch",
            "name": "[Prod] Helper service",
            "runtimeExecutable": "node",
            "runtimeArgs": [
                "helper_service/index.js"
            ],
            "envFile": "${workspaceRoot}/.vscode/.env_prod"
        },
        {
            "name": "[Prod] Launch Node.js in Docker",
            "type": "docker",
            "request": "launch",
            "preLaunchTask": "Run Docker Container",
            "platform": "node",
            "image": "node:latest",
            "envFile": "${workspaceRoot}/.vscode/.env_prod"
        },
        {
            "name": "[Prod] MRZ Passport Parser",
            "type": "go",
            "request": "launch",
            "mode": "auto",
            "program": "${workspaceRoot}/golang_services/mrz_parser",
            "args": [
                "--db-config",
                "ad_db"
            ],
            "envFile": "${workspaceRoot}/.vscode/.env_prod"
        },
        {
            "name": "[Prod] Node.js: Current File",
            "type": "node",
            "request": "launch",
            "program": "${file}",
            "console": "integratedTerminal",
            "cwd": "${workspaceFolder}"
        },
        {
            "type": "node",
            "request": "launch",
            "name": "[Prod] Nodejs Local",
            "runtimeExecutable": "nodemon",
            "args": [],
            "runtimeArgs": [
                "--exec",
                "node examples/send-sqs-queue/app.js"
            ]
        },
        {
            "name": "[Prod] Notification",
            "type": "go",
            "request": "launch",
            "mode": "auto",
            "program": "${workspaceRoot}/golang_services/notification",
            "args": [
                "n",
                "--ad_sqs",
                "ad_sqs",
                "--ad_aws",
                "ad_aws",
                "--ad_db",
                "ad_db",
                "--ad_s3",
                "ad_s3",
                "--ad_ses",
                "ad_ses"
            ],
            "envFile": "${workspaceRoot}/.vscode/.env_prod"
        },
        {
            "name": "[Prod] Notification User",
            "type": "go",
            "request": "launch",
            "mode": "auto",
            "program": "${workspaceRoot}/golang_services/notification-user",
            "args": [
                "n",
                "--ad_sqs",
                "ad_sqs",
                "--ad_aws",
                "ad_aws",
                "--ad_email",
                "ad_email",
                "--ad_website",
                "ad_website",
                "--ad_api_token",
                "ad_api_token",
                "--localize-file",
                "../conf/localize.yaml"
            ],
            "envFile": "${workspaceRoot}/.vscode/.env_prod"
        },
        {
            "name": "[Prod] Packages",
            "type": "go",
            "request": "launch",
            "mode": "auto",
            "program": "${workspaceRoot}/golang_services/packages",
            "buildFlags": "--buildvcs=false",
            "args": [
                "svc",
                "--db-config",
                "db-config",
                "--s3-bucket-config",
                "s3-bucket-config",
                "--sqs-config",
                "sqs-config",
                "--conf-file",
                "../conf/packages.yaml",
                "--localize-file",
                "../conf/localize.yaml",
                "--client-secret",
                "aaaa",
                "--ad_secrets",
                "ad_secrets_1",
                "--internal-auth-service-host",
                "bbbb",
                "--service-config",
                "packages_svc_config",
                "--account-service-config",
                "account-service-config",
                "--ad_packer_service",
                "ad_packer_service",
                "--ad_endpoint",
                "ad_endpoint"
            ],
            "envFile": "${workspaceRoot}/.vscode/.env_prod",
            "cwd": "${workspaceRoot}/golang_services/packages"
        },
        {
            "type": "node",
            "request": "launch",
            "name": "[Prod] Packer Offline",
            "runtimeExecutable": "nodemon",
            "runtimeArgs": [
                "--exec",
                "node src/index.js"
            ],
            "cwd": "${workspaceFolder}/nodejs_services/packer_offline",
            "envFile": "${workspaceRoot}/.vscode/.env_prod"
        },
        {
            "type": "node",
            "request": "launch",
            "name": "[Prod] Packer Online",
            "runtimeExecutable": "nodemon",
            "runtimeArgs": [
                "--exec",
                "node src/index.js"
            ],
            "cwd": "${workspaceFolder}/nodejs_services/packer_online",
            "envFile": "${workspaceRoot}/.vscode/.env_prod"
        },
        {
            "name": "[Prod] Packer Service",
            "type": "go",
            "request": "launch",
            "mode": "auto",
            "program": "${workspaceRoot}/golang_services/packer",
            "args": [
                "p",
                "--ad_db",
                "ad_db",
                "--ad_aws",
                "ad_aws",
                "--ad_sqs",
                "ad_sqs",
                "--ad_s3",
                "ad_s3",
                "--ad_packer_service",
                "ad_packer_service",
                "--ad_user_account_service",
                "ad_user_account_service",
                "--ad_secrets",
                "ad_secrets",
                "--ad_api_token",
                "ad_api_token"
            ],
            "envFile": "${workspaceRoot}/.vscode/.env_prod"
        },
        {
            "type": "node",
            "request": "launch",
            "name": "[Prod] Passport photo",
            "runtimeExecutable": "nodemon",
            "runtimeArgs": [
                "--exec",
                "node src/index.js"
            ],
            "cwd": "${workspaceRoot}/nodejs_services/passport_photo",
            "envFile": "${workspaceRoot}/.vscode/.env_prod"
        },
        {
            "name": "[Prod] Payment",
            "type": "go",
            "request": "launch",
            "mode": "auto",
            "program": "${workspaceRoot}/golang_services/payments",
            "args": [
                "svc",
                "--db-config",
                "ad_db",
                "--use-wechat-pay-prod",
                "--conf-file",
                "../conf/payments.yaml",
                "--service-config",
                "ad_payment_service",
                "--sqs-config",
                "sqs-config",
                "--account-service-config",
                "ad_user_account_service",
                "--ad_secrets",
                "ad_secrets",
                "--ad_website",
                "ad_website",
                "--ad_authorize",
                "ad_authorize",
                "--ad_wechatpay",
                "ad_wechatpay",
                "--ad_onepay",
                "ad_onepay",
                "--ad_paypal",
                "ad_paypal",
                "--ad_zellepay",
                "ad_zellepay",
                "--ad_stripe",
                "ad_stripe",
                "--version-file-bucket",
                "ad-app-version",
                "--region-payment-config-file",
                "ad_config_file"
            ],
            "envFile": "${workspaceRoot}/.vscode/.env_prod"
        },
        {
            "name": "[Prod] Payment worker",
            "type": "go",
            "request": "launch",
            "mode": "auto",
            "program": "${workspaceRoot}/golang_services/payment_worker",
            "preLaunchTask": "get_env",
            "args": [
                "svc",
                "--db-config",
                "ad_db",
                "--use-wechat-pay-prod",
                "--use-authorize-prod",
                "--conf-file",
                "../conf/payments.yaml",
                "--ad_authorize",
                "ad_authorize",
                "--ad_wechatpay",
                "ad_wechatpay",
                "--ad_onepay",
                "ad_onepay"
            ],
            "envFile": "${workspaceRoot}/.vscode/.env_prod"
        },
        {
            "name": "[Prod] Python: Current File",
            "type": "python",
            "request": "launch",
            "program": "${file}",
            "console": "integratedTerminal",
            "justMyCode": true
        },
        {
            "name": "[Prod] Search",
            "type": "go",
            "request": "launch",
            "mode": "auto",
            "program": "${workspaceRoot}/golang_services/search",
            "args": [
                "s",
                "--ad_es",
                "ad_es",
                "--ad_aws",
                "ad_aws",
                "--ad_user_account_service",
                "ad_user_account_service",
                "--ad_secrets",
                "ad_secrets"
            ],
            "envFile": "${workspaceRoot}/.vscode/.env_prod"
        },
        {
            "name": "[Prod] Tests",
            "type": "go",
            "request": "launch",
            "mode": "test",
            "program": "${fileDirname}",
            "env": {},
            "args": [
                "^TestGenerateConfigurationFailure$"
            ]
        },
        {
            "name": "[Prod] Tests",
            "type": "go",
            "request": "launch",
            "mode": "test",
            "remotePath": "",
            "program": "${fileDirname}",
            "env": {},
            "args": [
                "^TestGenerateConfigurationFailure$"
            ],
            "envFile": "${workspaceRoot}/.vscode/.env_prod"
        },
        {
            "name": "[Prod] Travel Photo",
            "type": "go",
            "request": "launch",
            "mode": "auto",
            "program": "${workspaceRoot}/golang_services/travel-photo",
            "args": [
                "--db-config",
                "db-config"
            ],
            "envFile": "${workspaceRoot}/.vscode/.env_prod"
        },
        {
            "name": "[Prod] User authorization",
            "type": "go",
            "request": "launch",
            "mode": "auto",
            "program": "${workspaceRoot}/golang_services/user-authentication",
            "args": [
                "svc",
                "--use-wechat-pay-prod",
                "--conf-file",
                "--ad_sqs",
                "ad_sqs",
                "--ad_s3",
                "ad_s3",
                "--ad_user_account_service",
                "account-service-config"
            ],
            "envFile": "${workspaceRoot}/.vscode/.env_prod"
        },
        {
            "name": "[Prod] Watchdog",
            "type": "go",
            "request": "launch",
            "mode": "auto",
            "program": "${workspaceRoot}/golang_services/watchdog",
            "args": [
                "svc",
                "--service-config",
                "ad_watchdog_service",
                "--sqs-config",
                "ad_sqs",
                "--ad_secrets",
                "ad_secrets",
                "--ad_fedex",
                "ad_fedex",
                "-ad-s3",
                "ad_s3"
            ],
            "envFile": "${workspaceRoot}/.vscode/.env_prod"
        },
        {
            "name": "[Prod] album_photo",
            "type": "go",
            "request": "launch",
            "mode": "auto",
            "program": "${workspaceRoot}/golang_services/album_photo",
            "args": [
                "svc",
                "--db-config",
                "db-config",
                "--ad_aws",
                "ad_aws",
                "--ad_secrets",
                "ad_secrets",
                "--account-service-config",
                "account-service-config",
                "--s3-bucket-config",
                "s3-bucket-config"
            ],
            "envFile": "${workspaceRoot}/.vscode/.env_prod"
        },
        {
            "name": "[Prod] master_data",
            "type": "go",
            "request": "launch",
            "mode": "auto",
            "program": "${workspaceRoot}/golang_services/master_data",
            "args": [
                "svc",
                "--db-config",
                "db-config",
                "--ad_secrets",
                "ad_secrets",
                "--account-service-config",
                "account-service-config"
            ],
            "envFile": "${workspaceRoot}/.vscode/.env_prod"
        },
        {
            "name": "[Prod] shipments",
            "type": "go",
            "request": "launch",
            "mode": "auto",
            "program": "${workspaceRoot}/golang_services/shipments",
            "args": [
                "service",
                "--db-config",
                "ad_db"
            ],
            "envFile": "${workspaceRoot}/.vscode/.env_prod"
        },
        {
            "name": "[Prod] submit-dispatcher",
            "type": "go",
            "request": "launch",
            "mode": "auto",
            "program": "${workspaceRoot}/golang_services/submit-dispatcher",
            "args": [
                "s",
                "--ad_db",
                "ad_db",
                "--ad_sqs",
                "ad_sqs",
                "--ad_aws",
                "ad_aws",
                "--ad_package_service",
                "ad_package_service",
                "--ad_s3",
                "ad_s3",
                "--ad_email",
                "ad_email",
                "--ad_website",
                "ad_website",
                "--localize-file",
                "../conf/localize.yaml"
            ],
            "envFile": "${workspaceRoot}/.vscode/.env_prod"
        },
        {
            "name": "[Prod] submit-email-worker",
            "type": "go",
            "request": "launch",
            "mode": "auto",
            "program": "${workspaceRoot}/golang_services/submit-email-worker",
            "args": [
                "s",
                "--ad_sqs",
                "ad_sqs",
                "--ad_aws",
                "ad_aws",
                "--ad_s3",
                "ad_s3",
                "--ad_submit_services",
                "ad_submit_services",
                "--localize-file",
                "../conf/localize.yaml",
                "--ad_db",
                "ad_db",
                "--ad_ses",
                "ad_ses",
                "--ad_email",
                "ad_email",
                "--ad_website",
                "ad_website",
                "--ad_api_token",
                "ad_api_token"
            ],
            "envFile": "${workspaceRoot}/.vscode/.env_prod"
        },
        {
            "type": "node",
            "request": "launch",
            "name": "[Prod] third_party_service",
            "runtimeExecutable": "node",
            "runtimeArgs": [
                "index.js"
            ],
            "cwd": "${workspaceFolder}/nodejs_services/third_party_service",
            "envFile": "${workspaceRoot}/.vscode/.env_prod"
        }
    ]
}