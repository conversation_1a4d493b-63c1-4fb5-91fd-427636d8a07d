FROM golang:1.18-bullseye AS builder
WORKDIR /aria

# Get dependancies - will also be cached if we won't change mod/sum, for faster builds
COPY golang_services/go.mod .
COPY golang_services/go.sum .
RUN go mod download

COPY ./ /aria
RUN cd golang_services/command_script/ && go build -buildvcs=false -o ../bin/command_script

FROM python:3.9-slim-bullseye
RUN apt-get update && apt-get install -y postgresql gcc libpq-dev
RUN python3 -m pip install boto3 psycopg2 numpy==1.21.6 pandas==1.5 openpyxl==3.1.0 gspread

WORKDIR /apps

COPY --from=builder /aria/infrastructure/databases/tools/ .
RUN mkdir html
COPY --from=builder /aria/golang_services/command_script/html/ ./html/
COPY --from=builder /aria/golang_services/bin/command_script .
