FROM golang:1.23-alpine AS builder
RUN apk add bash git make
WORKDIR /aria

# Get dependancies - will also be cached if we won't change mod/sum, for faster builds
COPY golang_services/go.mod .
COPY golang_services/go.sum .
RUN go mod download

COPY ./ /aria
ENV GOPROXY=https://goproxy.cn,https://goproxy.io,direct
RUN cd golang_services/email-hook/ && go build -buildvcs=false -o ../bin/email-hook

FROM alpine:3.12
RUN apk add ca-certificates
WORKDIR /apps
COPY --from=builder /aria/golang_services/bin/email-hook .