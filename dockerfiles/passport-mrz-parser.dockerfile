FROM node:11.8.0-alpine

RUN apk add \
    python \
    g++ \
    build-base \
    cairo-dev \
    jpeg-dev \
    pango-dev \
    musl-dev \
    giflib-dev \
    pixman-dev \
    pangomm-dev \
    libjpeg-turbo-dev \
    freetype-dev \
    && npm install canvas@2.6.0

WORKDIR /usr/src/app

COPY ./nodejs_services/passport_mrz_parser/package*.json ./

RUN npm install

COPY ./nodejs_services/passport_mrz_parser/ .

ENV PORT 3000

EXPOSE ${PORT}

CMD [ "npm", "start" ]

