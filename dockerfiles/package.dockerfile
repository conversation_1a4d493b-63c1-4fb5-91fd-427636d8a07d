FROM golang:1.23-alpine AS builder
RUN apk add bash git make
WORKDIR /aria

# Get dependancies - will also be cached if we won't change mod/sum, for faster builds
COPY golang_services/go.mod .
COPY golang_services/go.sum .
RUN go mod download

COPY ./ /aria
# ENV GOPROXY=https://goproxy.cn,https://goproxy.io,direct
RUN cd golang_services/packages/ && go build -buildvcs=false -o ../bin/packages

FROM alpine:3.12
RUN apk add ca-certificates tzdata
WORKDIR /apps
COPY --from=builder /aria/golang_services/bin/packages .
COPY --from=builder /aria/golang_services/conf/packages.yaml conf/
COPY --from=builder /aria/golang_services/conf/localize.yaml conf/
COPY --from=builder /aria/template_files/ .
COPY --from=builder /aria/golang_services/packages/fonts/ fonts/
ADD https://github.com/leowilbur/helper-binary/raw/refs/heads/master/check_email_exist_alpine /apps/check_email_exist
RUN chmod +x /apps/check_email_exist