FROM python:3.8.0-slim
LABEL maintainer=ARIADIRECT

RUN apt-get update && \
    apt-get install -y gcc && \
    pip install --upgrade --no-cache-dir pip

RUN apt-get install ffmpeg libsm6 libxext6  -y
RUN pip install pip --upgrade

WORKDIR /src

COPY python_services/id_reader/src /src

COPY python_services/id_reader/src/models /models

COPY python_services/id_reader/requirements.txt /requirements.txt

RUN pip install -r /requirements.txt
