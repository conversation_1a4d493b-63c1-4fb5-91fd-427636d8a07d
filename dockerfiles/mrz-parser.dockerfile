FROM ubuntu:latest as builder
RUN apt-get update
RUN apt-get install -y wget git gcc

RUN wget -P /tmp "https://dl.google.com/go/go1.23.1.linux-amd64.tar.gz"

RUN tar -C /usr/local -xzf "/tmp/go1.23.1.linux-amd64.tar.gz"
RUN rm "/tmp/go1.23.1.linux-amd64.tar.gz"

ENV GOPATH /go
ENV PATH $GOPATH/bin:/usr/local/go/bin:$PATH
RUN mkdir -p "$GOPATH/src" "$GOPATH/bin" && chmod -R 777 "$GOPATH"

WORKDIR /aria

# Get dependancies - will also be cached if we won't change mod/sum, for faster builds
COPY golang_services/go.mod .
COPY golang_services/go.sum .
# ENV GOPROXY=https://goproxy.cn,https://goproxy.io,direct

RUN go mod download
# RUN go build -tags extlib


COPY ./ /aria
ENV GOPROXY=https://goproxy.cn,https://goproxy.io,direct
RUN cd golang_services/mrz_parser/ && go build -buildvcs=false -o ../bin/mrz_parser

FROM ubuntu:latest
WORKDIR /apps
RUN apt-get update
RUN apt-get install -y ca-certificates
COPY --from=builder /aria/golang_services/bin/mrz_parser .