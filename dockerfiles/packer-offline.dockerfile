FROM node:10.7.0-alpine AS base
ENV CHROME_BIN="/usr/bin/chromium-browser" \
    PUPPETEER_SKIP_CHROMIUM_DOWNLOAD="true"
RUN set -x \
    && apk update \
    && apk upgrade \
    && apk add \
    alpine-sdk \
    python \
    pdftk \
    ocaml \
    libelf-dev \
    udev \
    ttf-freefont \
    chromium \
    msttcorefonts-installer fontconfig && \
    update-ms-fonts && \
    fc-cache -f 

RUN apk fetch openjdk8
RUN apk add openjdk8
ENV JAVA_HOME=/usr/lib/jvm/java-1.8-openjdk
ENV PATH="$JAVA_HOME/bin:${PATH}"

RUN java -version
RUN javac -version

WORKDIR /usr/src/app

COPY ./nodejs_services/packer_offline/package*.json ./


COPY ./nodejs_services/packer_offline/ .

RUN rm node_modules -rf

RUN npm install

ENV PORT 5060

EXPOSE ${PORT}

CMD [ "npm", "start" ]