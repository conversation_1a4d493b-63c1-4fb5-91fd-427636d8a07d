FROM python:3.6-slim
LABEL maintainer=ARIADIRECT

RUN apt-get update && \
    pip --no-cache-dir install --upgrade pip

RUN apt-get install ffmpeg libsm6 libxext6  -y

WORKDIR /src

COPY python_services/atlas_worker/src /src

COPY python_services/atlas_worker/src/models /models

COPY python_services/atlas_worker/requirements.txt /requirements.txt

RUN pip install -r /requirements.txt

CMD python /src/atlas.py
