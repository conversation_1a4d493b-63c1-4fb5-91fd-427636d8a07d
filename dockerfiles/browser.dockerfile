FROM alpine:3.18

RUN apk add --no-cache \
    nodejs npm \
    chromium \
    nss \
    freetype \
    freetype-dev \
    harfbuzz \
    ca-certificates \
    ttf-freefont

ENV PUPPETEER_SKIP_CHROMIUM_DOWNLOAD=true \
    PUPPETEER_EXECUTABLE_PATH=/usr/bin/chromium-browser

WORKDIR /app

COPY ./nodejs_services/browser_service/package.json .

RUN npm install

COPY ./nodejs_services/browser_service/ .

CMD ["node", "app.js"]