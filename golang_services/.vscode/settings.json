{"cSpell.words": ["etsp", "gjson", "gorm", "govalidator", "ILIKE", "linkedin", "metas", "pdfg", "tesseract", "tsquery", "tsvector", "wkhtmltopdf", "ZELLEPAY"], "cSpell.ignoreWords": ["a", "address", "agg", "airport", "and", "application", "applications", "as", "at", "attributes", "build", "by", "c", "cart", "case", "category", "coalesce", "completed", "config", "consulate", "contact", "count", "country", "created", "current", "customer", "data", "deleted", "desc", "e", "else", "end", "ep", "ets", "fastlane", "files", "from", "group", "id", "in", "info", "input", "is", "issue", "item", "join", "jsonb", "label", "last", "left", "limit", "method", "nationality", "notification", "null", "num", "object", "of", "on", "open", "order", "orders", "org", "output", "p", "pa", "package", "payment", "pending", "pods", "price", "prices", "process", "product", "provider", "purpose", "receive", "region", "residence", "select", "service", "services", "shipment", "shipping", "sl", "so", "status", "submit", "submitted", "summary", "t", "tag", "tasks", "text", "then", "time", "to", "tracking", "true", "type", "updated", "user", "v", "vcp", "visa", "vp", "vs", "when", "where"], "nuxt.isNuxtApp": false, "ansible.python.interpreterPath": "/bin/python3"}