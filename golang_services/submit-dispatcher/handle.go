package main

import (
	"fmt"
	"net/url"

	"github.com/robfig/cron"
	"github.com/rs/zerolog/log"

	"bitbucket.org/persistence17/aria/golang_services/localize"
	"bitbucket.org/persistence17/aria/golang_services/models"
	awslib "bitbucket.org/persistence17/aria/golang_services/sdk/aws"
	"bitbucket.org/persistence17/aria/golang_services/sdk/db"
	"bitbucket.org/persistence17/aria/golang_services/sdk/utils"
)

const (
	NotificationMethod                   = "email"
	FillMissingPackageEmailTemplateName  = "noti_fill_missing_package_to_ad"
	DateFormat                           = "Jan 02, 2006"
	DateTimeFormat                       = "2006-01-02T15:04:05"
	SendETSRequestToProviderTemplateName = "ets_request_to_provider"
	CheckistListTemplateName             = "visa_package_checklist"
	IssueMethosVOA                       = "voa"
	ProductionEnv                        = "prod"
)

type SubmitDispatcher struct {
	dao             *db.Dao
	paymentDao      *db.PaymentsDao
	etsDao          *db.EtsDao
	shipDao         *db.ShippingDataDao
	sqsSubmit       *awslib.SQSClient
	sqsShip         *awslib.SQSClient
	config          map[string]any
	s3Presigner     *awslib.S3Svc
	s3Config        map[string]any
	localize        *localize.Localize
	sqsNotification *awslib.SQSClient
	s3Downloader    *awslib.S3Downloader
}

func NewSubmitDispatcher(conn *db.AuroraDB, sqsSubmit *awslib.SQSClient, sqsShip *awslib.SQSClient, config map[string]any, s3Presigner *awslib.S3Svc, s3Downloader *awslib.S3Downloader, s3Config map[string]any, localize *localize.Localize, sqsNotification *awslib.SQSClient) *SubmitDispatcher {
	dao := db.NewDaoWithDb(conn)
	paymentDao := db.NewPaymentDaoWithDb(conn)
	etsDao := db.NewEtsDao(conn)
	shipDao := db.NewShippingDataDao(conn)
	return &SubmitDispatcher{dao: dao, etsDao: etsDao, paymentDao: paymentDao, sqsSubmit: sqsSubmit, sqsShip: sqsShip, config: config, s3Presigner: s3Presigner, s3Config: s3Config, localize: localize, sqsNotification: sqsNotification, s3Downloader: s3Downloader, shipDao: shipDao}
}

func (s *SubmitDispatcher) WatchSubmitDispatcher() error {
	var tasks []*utils.Task
	tasks = append(tasks, utils.NewTask(func() error {
		select {} // Wait forever
	}))

	if err := s.ReceiptGenerator(); err != nil {
		fmt.Printf("ReceiptGenerator error: %v", err)
	}

	if err := s.ProcessSubmitETSOrder(); err != nil {
		fmt.Printf("ProcessSubmitETSOrder error: %v", err)
	}

	tasks = append(tasks, utils.NewTask(func() error {
		var cr = cron.New()
		running := false
		if err := cr.AddFunc("@every 2m", func() {
			if running {
				return
			}
			running = true
			if err := s.ReceiptGenerator(); err != nil {
				fmt.Printf("ReceiptGenerator error: %v", err)
			}
			running = false
		}); err != nil {
			return err
		}

		cr.Start()
		return nil
	}))

	tasks = append(tasks, utils.NewTask(func() error {
		var cr = cron.New()
		running := false
		if err := cr.AddFunc("@every 1m", func() {
			if running {
				return
			}
			running = true
			if err := s.ProcessSubmitETSOrder(); err != nil {
				fmt.Printf("ProcessSubmitETSOrder error: %v", err)
			}
			running = false
		}); err != nil {
			return err
		}

		cr.Start()
		return nil
	}))

	var p = utils.NewPool(tasks, 10) // Set max concurrency
	p.Run()

	if p.HasErrors() {
		for _, task := range p.Tasks {
			if task.Err != nil {
				log.Error().Msgf("Task return error %v", task.Err)
			}
		}
		return fmt.Errorf("Pool error running, please check log! ")
	}
	return nil
}

// Get all success payment with no receipt then generate receipt
func (s *SubmitDispatcher) ReceiptGenerator() error {
	queryPayment := url.Values{}
	queryPayment.Add("status", models.PaymentStatusSuccess)
	queryPayment.Add("receipt_id", "")
	queryPayment.Add("cart_id", "")
	payments, err := s.paymentDao.QueryPayments(queryPayment)
	if err != nil {
		return err
	}
	if len(payments) != 0 {
		for _, p := range payments {
			if err := s.SendReceipt(p); err != nil {
				log.Info().Msg(err.Error())
			}
		}
	}
	return nil
}

// Process submit ets order
func (s *SubmitDispatcher) ProcessSubmitETSOrder() error {
	orders, err := s.etsDao.GetServiceOrders(map[string][]any{"status": {models.EtsOrderStatusPaid, models.EtsOrderStatusDispatched, models.EtsOrderStatusCreatedShipmentUser}})
	// orders, err := s.etsDao.GetServiceOrders(map[string][]any{"id": {6738}})
	if err != nil {
		return err
	}
	if len(orders) != 0 {
		for _, item := range orders {
			fmt.Printf("Process submit ets order: %d, status: %s \n", item.ID, item.Status)
			if err = s.ServiceProcess(item); err != nil {
				log.Info().Msg(err.Error())
			}
		}
	}
	return nil
}
