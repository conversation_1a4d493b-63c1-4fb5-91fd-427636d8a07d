package main

import (
	"encoding/json"
	"fmt"
	"strconv"
	"strings"
	"time"

	"bitbucket.org/persistence17/aria/golang_services/sdk/applications"
	"bitbucket.org/persistence17/aria/golang_services/sdk/fedex"
	"bitbucket.org/persistence17/aria/golang_services/sdk/notification"
	"bitbucket.org/persistence17/aria/golang_services/sdk/product"
	"bitbucket.org/persistence17/aria/golang_services/sdk/time_util"
	"bitbucket.org/persistence17/aria/golang_services/sdk/utils"
	"bitbucket.org/persistence17/aria/golang_services/sdk/zalo"

	"bitbucket.org/persistence17/aria/golang_services/models"
	"bitbucket.org/persistence17/aria/golang_services/sdk/shipment"
	"github.com/metal3d/go-slugify"
	"github.com/rs/zerolog/log"
	"github.com/samber/lo"
	"github.com/spf13/cast"
	"github.com/thoas/go-funk"
	"github.com/tidwall/gjson"
)

func (s *SubmitDispatcher) ServiceProcess(order *models.ServiceOrder) error {
	svcs, err := s.etsDao.QueryExtendedTravelServices(map[string]any{"id": order.ServiceID}, 0, 1)
	if err != nil {
		return err
	}
	if len(svcs) == 0 {
		return nil
	}
	svc := svcs[0]

	switch svc.ServiceType {
	case models.EtsServiceTypeFastlane:
		if order.Status == models.EtsOrderStatusPaid {
			err := s.FastlaneServiceProcess(order)
			if err != nil {
				return err
			}
		}
		return nil
	case models.EtsServiceTypePassport:
		if err := s.PassportServiceProcess(order); err != nil {
			return err
		}
		return nil
	default:
		if err := s.OtherServiceProcess(order); err != nil {
			return err
		}
		return nil
	}
}

func (s *SubmitDispatcher) FastlaneServiceProcess(order *models.ServiceOrder) error {
	svcs, err := s.etsDao.QueryExtendedTravelServices(map[string]any{"id": order.ServiceID}, 0, 1)
	if err != nil {
		return err
	}
	svc := svcs[0]
	attrJ := utils.StructToJSON(svc.Attributes)

	tasks, _, err := s.etsDao.QueryServiceTasks(map[string]any{"order_id": order.ID}, 0, 1)
	if err != nil {
		return err
	}

	orderForms := []string{}
	taskForms := map[int64][]string{}
	for _, form := range attrJ.Get("form.order_forms").Array() {
		if attrJ.Get("form.form_detail").Get(form.String()).Get("auto_create").Bool() {
			orderForms = append(orderForms, form.String())
		}
	}

	for _, task := range tasks {
		if attrJ.Get("form").Exists() {
			for _, form := range attrJ.Get("form.task_forms").Array() {
				if attrJ.Get("form.form_detail").Get(form.String()).Get("auto_create").Bool() {
					taskForms[task.ID] = append(taskForms[task.ID], form.String())
				}

			}
		}
	}

	if order.AutoGeneratedFormAt == nil {
		if err := s.etsDao.UpdateServiceOrder(map[string]any{"auto_generated_form_at": time.Now()}, order.ID); err != nil {
			return err
		}
		for _, task := range tasks {
			if len(taskForms[task.ID]) > 0 {
				if err := s.etsDao.UpdateServiceTask(map[string]any{"status": models.EtsTaskStatusFormCreating}, cast.ToInt64(task.ID)); err != nil {
					return err
				}
			} else {
				if err := s.etsDao.UpdateServiceTask(map[string]any{"status": models.EtsTaskStatusConfirmed}, cast.ToInt64(task.ID)); err != nil {
					return err
				}
			}
		}

		if len(taskForms) > 0 || len(orderForms) > 0 {
			if err := applications.CallToGenerateETSForm(order.ID, orderForms, taskForms); err != nil {
				return err
			}
		}
	}

	if !IsAllFormGenerated(svc, order, tasks) {
		return nil
	}

	user, err := s.dao.GetUserByID(order.UserID)
	if err != nil {
		return err
	}

	servicePrices, err := s.etsDao.GetServicePrice([]int{order.ServiceID})
	if err != nil {
		return err
	}
	servicePrice := servicePrices[order.ServiceID][0]
	provider, err := s.etsDao.GetEtsProviderByID(servicePrice.ProviderID)
	if err != nil {
		return err
	}
	if provider == nil {
		return fmt.Errorf("not found provider %s in order %d", servicePrice.ProviderID, order.ServiceID)
	}

	shipment, err := s.dao.GetVisaShipment(order.ShipmentInfo.String)
	if err != nil {
		return err
	}
	shipmentJ := utils.StructToJSON(shipment).Get("shipping_contact")

	for _, task := range tasks {
		inputPods := task.InputPods.ToMapKeyValueV2(order.InputPods)
		noOfTraveler := 1
		if order.Summary != nil {
			noOfTraveler = order.Summary.Quantity
		}

		fullName := "SIR/MADAM"
		if user.GivenName != "" {
			fullName = strings.ToUpper(fmt.Sprintf("%s %s", strings.TrimSpace(user.GivenName), strings.TrimSpace(user.Surname)))
		} else if shipmentJ.Get("given_name").String() != "" {
			fullName = strings.ToUpper(fmt.Sprintf("%s %s", strings.TrimSpace(shipmentJ.Get("given_name").String()), strings.TrimSpace(shipmentJ.Get("surname").String())))
		}

		isDeparture := svc.Tasks[0] == "departure"
		meetupTime := lo.If(isDeparture, cast.ToTime(inputPods["travel_exit_flight_meetup_time"]).Format("Jan 02, 2006 - 15:04")).Else("")

		parameters := map[string]any{
			"OrderID":         order.ID,
			"FullName":        strings.ToUpper(fullName),
			"ProviderName":    strings.ToUpper(provider.Name),
			"Country":         s.localize.EN.Country[svc.Country],
			"ServiceType":     s.localize.EN.ServiceType[svc.ServiceType],
			"NoOfTraveler":    fmt.Sprintf("%d", noOfTraveler),
			"ListOfTraveler":  cast.ToString(inputPods["travel_passenger_info_passenger_name_list"]),
			"MeetupTime":      meetupTime,
			"Terminal":        s.localize.EN.Terminal[attrJ.Get("terminal").String()],
			"AirportName":     svc.Airport,
			"ProcessingTime":  s.localize.EN.ProcessingTime[attrJ.Get("processing_time").String()],
			"Task":            s.localize.EN.Tasks[strings.Join(svc.Tasks, ",")],
			"FlightNo":        utils.GetStringOrText(inputPods["travel_exit_flight_exit_flight"]),
			"ServiceDateTime": inputPods["travel_exit_flight_exit_timestamp"],
			"URL":             fmt.Sprintf("%s/dashboard/customer-orders/detail?order_id=%d&service=ets", s.config["website_host_name"].(string), order.ID),
		}

		if task.Type == "arrival" || task.Type == "vip_arrival" {
			parameters["FlightNo"] = utils.GetStringOrText(inputPods["travel_enter_flight_enter_flight"])
			parameters["ServiceDateTime"] = inputPods["travel_enter_flight_enter_timestamp"]
		}
		parameters["ServiceDateTime"] = utils.StructToJSON(parameters).Get("ServiceDateTime").Time().Format("Mon, 02 Jan 2006 15:04")

		// Get airport name
		if svc.Airport != nil {
			airlines, err := s.dao.GetMasterDataValueByName("airport")
			if err != nil {
				return err
			}
			result := gjson.GetBytes(airlines, fmt.Sprintf(`#(iata="%s").name`, *svc.Airport))
			if val := result.String(); val != "" {
				parameters["AirportName"] = val + " (" + *svc.Airport + ")"
			}
		}

		sendToUserTemplate := attrJ.Get("email_template.send_to_user").String()
		// 1. send ets confirmation email to user
		if sendToUserTemplate == "" {
			return fmt.Errorf("not found email template to send the user in product %d", order.ServiceID)
		}

		// User using this format url
		parameters["URL"] = fmt.Sprintf("%s/dashboard/customer-orders/detail?order_id=%d&service=ets", s.config["website_host_name"].(string), order.ID)

		msgConfirm := map[string]any{
			"template_name": sendToUserTemplate,
			"to":            user.Email,
			"bcc":           []string{s.config["services_email"].(string)},
			"parameters":    parameters,
		}

		shipmentEmail := shipmentJ.Get("email").String()
		if shipmentEmail != "" && shipmentEmail != user.Email {
			msgConfirm["cc"] = []string{shipmentEmail}
		}

		err = s.sqsNotification.Send(msgConfirm)
		if err != nil {
			return err
		}
		// 2. send ets request email to provider
		if s.config["env"].(string) != ProductionEnv {
			provider.Contact.Email = s.config["services_email"].(string)
		}

		sendToProviderTemplate := attrJ.Get("email_template.send_to_provider").String()
		if sendToProviderTemplate == "" {
			return fmt.Errorf("not found email template to send the provider in product %d", order.ServiceID)
		}

		// Provider using this format url
		parameters["URL"] = fmt.Sprintf("%s/dashboard/orders/detail?order_id=%d&service=ets", s.config["website_host_name"].(string), order.ID)

		msgRequest := map[string]any{
			"template_name": sendToProviderTemplate,
			"to":            provider.Contact.Email,
			"bcc":           []string{s.config["services_email"].(string)},
			"parameters":    parameters,
		}
		if err = s.sqsNotification.Send(msgRequest); err != nil {
			return err
		}
		// 3. update status of service order
		if err = s.etsDao.UpdateServiceOrder(map[string]any{"status": models.EtsOrderStatusSubmitted, "submitted_time": time.Now()}, order.ID); err != nil {
			return err
		}

		// 4. Auto assign staff
		if err := AutoAssignedOrderStaffToEtsOrder(int64(order.ID), s.etsDao); err != nil {
			return err
		}

		if err := s.etsDao.CreateWatchdogJob(models.WatchDogJob{
			JobName:    models.JobNameTriggerSendUpdatedFastlaneOrder,
			JobData:    []byte(utils.StructToJSON(map[string]any{}).Raw),
			ScheduleAt: time.Now(),
		}); err != nil {
			fmt.Println(err)
		}
	}

	return nil
}

func (s *SubmitDispatcher) OtherServiceProcess(order *models.ServiceOrder) error {
	tasks, _, err := s.etsDao.QueryServiceTasks(map[string]any{"order_id": order.ID}, 0, 0)
	if err != nil {
		return err
	}
	svcs, err := s.etsDao.QueryExtendedTravelServices(map[string]any{"id": order.ServiceID}, 0, 1)
	if err != nil {
		return err
	}
	svc := svcs[0]
	attrJ := utils.StructToJSON(svc.Attributes)

	orderForms := []string{}
	taskForms := map[int64][]string{}
	for _, form := range attrJ.Get("form.order_forms").Array() {
		if attrJ.Get("form.form_detail").Get(form.String()).Get("auto_create").Bool() {
			orderForms = append(orderForms, form.String())
		}
	}

	for _, task := range tasks {
		if attrJ.Get("form").Exists() {
			for _, form := range attrJ.Get("form.task_forms").Array() {
				if attrJ.Get("form.form_detail").Get(form.String()).Get("auto_create").Bool() {
					taskForms[task.ID] = append(taskForms[task.ID], form.String())
				}

			}
		}
	}

	if order.AutoGeneratedFormAt == nil {
		if err := s.etsDao.UpdateServiceOrder(map[string]any{"auto_generated_form_at": time.Now()}, order.ID); err != nil {
			return err
		}
		// Preprocessing data before submit
		if err := s.ServicePreProcessing(order); err != nil {
			return err
		}

		for _, task := range tasks {
			if len(taskForms[task.ID]) > 0 {
				if err := s.etsDao.UpdateServiceTask(map[string]any{"status": models.EtsTaskStatusFormCreating}, cast.ToInt64(task.ID)); err != nil {
					return err
				}
			} else {
				if err := s.etsDao.UpdateServiceTask(map[string]any{"status": models.EtsTaskStatusConfirmed}, cast.ToInt64(task.ID)); err != nil {
					return err
				}
			}
		}

		if len(taskForms) > 0 || len(orderForms) > 0 {
			if err := applications.CallToGenerateETSForm(order.ID, orderForms, taskForms); err != nil {
				return err
			}
		}
	}

	if !IsAllFormGenerated(svc, order, tasks) {
		return nil
	}

	// step 1. Send email to user
	// step 2. Send email to provider
	// step 3. Update status from paid to submitted
	user, err := s.dao.GetUserByID(order.UserID)
	if err != nil {
		return err
	}

	servicePrices, err := s.etsDao.GetServicePrice([]int{order.ServiceID})
	if err != nil {
		return err
	}
	servicePrice := servicePrices[order.ServiceID][0]
	provider, err := s.etsDao.GetEtsProviderByID(servicePrice.ProviderID)
	if err != nil {
		return err
	}
	if provider == nil {
		return fmt.Errorf("not found provider %s in order %d", servicePrice.ProviderID, order.ServiceID)
	}

	resp, err := s.etsDao.QueryServiceOrders(models.ServiceOrderFilter{
		ID:              []string{strconv.Itoa(order.ID)},
		IncludeShipment: true,
		IncludeTasks:    true,
		Limit:           1,
	})
	if err != nil {
		return err
	}

	confJ := utils.StructToJSON(resp.Data[0].QueryPodValues)
	noOfTraveler := 1
	if order.Summary != nil {
		noOfTraveler = order.Summary.Quantity

	}
	inputPodJ := utils.StructToJSON(order.InputPods.ToMapKeyValue())

	shipment, err := s.dao.GetVisaShipment(order.ShipmentInfo.String)
	if err != nil {
		return err
	}
	shipmentJ := utils.StructToJSON(shipment).Get("shipping_contact")

	fullName := "SIR/MADAM"
	if user.GivenName != "" {
		fullName = strings.ToUpper(fmt.Sprintf("%s %s", strings.TrimSpace(user.GivenName), strings.TrimSpace(user.Surname)))
	} else if shipmentJ.Get("given_name").String() != "" {
		fullName = strings.ToUpper(fmt.Sprintf("%s %s", strings.TrimSpace(shipmentJ.Get("given_name").String()), strings.TrimSpace(shipmentJ.Get("surname").String())))
	}

	parameters := map[string]any{
		"OrderID":           order.ID,
		"FullName":          strings.ToUpper(fullName),
		"ProviderName":      strings.ToUpper(provider.Name),
		"Task":              s.localize.EN.Tasks[strings.Join(svc.Tasks, ",")],
		"ServiceType":       s.localize.EN.ServiceType[svc.ServiceType],
		"ProcessingTime":    s.localize.EN.ProcessingTime[attrJ.Get("processing_time").String()],
		"Validity":          s.localize.EN.Duration[utils.OneOf(attrJ.Get("validity").String())],
		"NumberOfEntry":     s.localize.EN.NumberOfEntry[attrJ.Get("number_of_entries").String()],
		"Purpose":           s.localize.EN.Purpose[utils.OneOf(attrJ.Get("purpose").String(), inputPodJ.Get("travel_visa_info_purpose").String())],
		"Country":           svc.Country,
		"NoOfTraveler":      fmt.Sprintf("%d", noOfTraveler),
		"ListOfTraveler":    "",
		"RegionOfResidence": s.localize.EN.Country[confJ.Get("service_core_info_region_of_residence").String()],
		"URL":               fmt.Sprintf("%s/dashboard/customer-orders/detail?order_id=%d&service=ets", s.config["website_host_name"].(string), order.ID),
	}

	country, _ := gocountry.FindCountryByAlpha(svc.Country)
	parameters["Country"] = country.Name.Common

	if svc.ServiceType == "country_tourist" {
		inputPodMap := resp.Data[0].Tasks[0].InputPods.ToMapByName("Name")
		// Get airport name
		if svc.Airport != nil {
			airlines, err := s.dao.GetMasterDataValueByName("airport")
			if err != nil {
				return err
			}
			result := gjson.GetBytes(airlines, fmt.Sprintf(`#(iata="%s").name`, *svc.Airport))
			if val := result.String(); val != "" {
				parameters["ArrivalAirport"] = val + " (" + *svc.Airport + ")"
			}
		}

		parameters["EnterDate"] = utils.StructToJSON(inputPodMap["enter_timestamp"]).Get("value.fe").Time().Format("Mon, 02 Jan 2006 15:04")
		parameters["ExitDate"] = utils.StructToJSON(inputPodMap["exit_timestamp"]).Get("value.fe").Time().Format("Mon, 02 Jan 2006 15:04")
		parameters["NoOfTasks"] = len(resp.Data[0].Tasks)

	}

	attachments := []map[string]any{}

	// Send request to create summary file
	if svc.ServiceType == "new_visa" || svc.ServiceType == "new_visa_urgent" {
		message := map[string]any{
			"product_type": "new_visa",
			"id":           order.ID,
			"method":       NotificationMethod,
			"callback":     fmt.Sprintf("https://%s/ets-internal/%d/add-output-file", s.config["package_host_name"].(string), order.ID),
		}
		//  func SubmitEmailWorker(message *sqs.Message)
		if err := s.sqsSubmit.Send(message); err != nil {
			log.Error().Str("error", err.Error()).Int("package_id", order.ID).Msg("failed to send to submit queue")
			return err
		}

		if svc.Tag != nil {
			boardingLetter := utils.StructToJSON(resp.Data[0].OutputFiles).Get("vnm_approval_letter.file_url").String()
			if boardingLetter != "" {
				bucket, key, err := utils.UrlToS3BucketAndKey(boardingLetter)
				if err != nil {
					return err
				}

				attachments = append(attachments, map[string]any{
					"bucket":    bucket,
					"key":       key,
					"file_name": utils.GetFileNameFromURL(boardingLetter),
				})
			}
		}
	}

	dataApplications := []map[string]any{}

	for _, task := range tasks {
		dataApplications = append(dataApplications, map[string]any{
			"FullName":          task.GetAppName(svc.ServiceType),
			"RegionOfResidence": s.localize.EN.Country[confJ.Get("service_core_info_region_of_residence").String()],
			"ProcessingTime":    s.localize.EN.ProcessingTime[attrJ.Get("processing_time").String()],
			"Task":              s.localize.EN.Tasks[strings.Join(svc.Tasks, ",")],
		})
	}
	parameters["Applications"] = dataApplications

	if svc.ServiceType == models.EtsServiceTypeGlobalEntry || svc.ServiceType == models.EtsServiceTypeFastTruck {
		parameters["Task"] = s.localize.EN.DocumentType[(*svc.Attributes)["document_type"].(string)]
	}
	emailTemplate, ok := (*svc.Attributes)["email_template"].(map[string]any)
	if !ok {
		return fmt.Errorf("not found email template in product %d", order.ServiceID)
	}
	// 1. send ets confirmation email to user
	sendToUserEmailTemplate := cast.ToString(emailTemplate["send_to_user"])

	if sendToUserEmailTemplate == "" {
		return fmt.Errorf("not found email template to send the user in product %d", order.ServiceID)
	}

	isSendApprovalLetter := false

	if attrJ.Get("form.form_detail.vnm_approval_letter").Exists() {
		airportsRaw, _ := s.dao.DB().GetMasterDataValueByName("airport")

		airports := []models.Airport{}
		json.Unmarshal(airportsRaw, &airports)
		exitAirport, ok := lo.Find(airports, func(a models.Airport) bool {
			return a.Iata == inputPodJ.Get("travel_exit_flight_exit_airport").String()
		})
		if ok {
			loc, err := time.LoadLocation(exitAirport.Timezone)
			vnLoc, _ := time.LoadLocation("Asia/Ho_Chi_Minh")
			diff, _ := time_util.DiffTwoTimeZone(loc, vnLoc)

			if err == nil {
				if inputPodJ.Get("travel_exit_flight_boarding_datetime").Exists() {

					minProcessingTime, _ := s.dao.DB().GetProcessingTimeWithInput(svc.ID, time.Now(), "2h")
					newMinProcessingTime := minProcessingTime.UTC().Add(time.Hour * 7).Add(time.Hour * 3)

					boardingTime := inputPodJ.Get("travel_exit_flight_boarding_datetime").Time()
					boardingTimeInVietnamAsUTC := boardingTime.Add(diff)
					if boardingTimeInVietnamAsUTC.Unix() <= newMinProcessingTime.Unix() {
						isSendApprovalLetter = true
					}
				}
			}
		}

	}

	msgConfirm := map[string]any{
		"template_name": sendToUserEmailTemplate,
		"to":            user.Email,
		"bcc":           []string{s.config["services_email"].(string)},
		"parameters":    parameters,
	}

	shipmentEmail := utils.StructToJSON(resp.Data[0]).Get("shipment.shipping_contact.email").String()
	if shipmentEmail != "" && shipmentEmail != user.Email {
		msgConfirm["cc"] = []string{shipmentEmail}
	}

	if err = s.sqsNotification.Send(msgConfirm); err != nil {
		return err
	}

	// Delay send approval letter
	if isSendApprovalLetter {
		approvalEmailTemplate := map[string]any{
			"template_name": "send_approval_letter_to_user",
			"to":            msgConfirm["to"],
			"bcc":           msgConfirm["bcc"],
			"parameters":    parameters,
			"attachments":   attachments,
		}
		if msgConfirm["cc"] != nil {
			approvalEmailTemplate["cc"] = msgConfirm["cc"]
		}
		if err := s.etsDao.CreateWatchdogJob(models.WatchDogJob{
			JobName:    models.JobNameDelaySendEmail,
			JobData:    []byte(utils.StructToJSON(approvalEmailTemplate).Raw),
			ScheduleAt: time.Now().Add(15 * time.Minute),
		}); err != nil {
			return err
		}

		vnLoc, _ := time.LoadLocation("Asia/Ho_Chi_Minh")
		sendTime := time.Now().Add(15 * time.Minute).In(vnLoc)
		zalo.SendZaloSMS(zalo.GetGroupID("INTERNAL_NOTIFICATION"), fmt.Sprintf("Approval letter for order: %d will send to user at: %s", order.ID, sendTime.Format("Jan 02, 2006 - 15:04")))
	} else {
		zalo.SendZaloSMS(zalo.GetGroupID("INTERNAL_NOTIFICATION"), fmt.Sprintf("Order: %d skip send approval letter", order.ID))
	}

	// 2. send ets request email to provider
	if s.config["env"].(string) != ProductionEnv {
		provider.Contact.Email = s.config["services_email"].(string)
	}
	if sendToProviderEmailTemplate, ok2 := emailTemplate["send_to_provider"]; !ok2 || sendToProviderEmailTemplate == "" {
		return fmt.Errorf("not found email template to send the provider in product %d", order.ServiceID)
	}

	parameters["URL"] = fmt.Sprintf("%s/dashboard/orders/detail?order_id=%d&service=ets", s.config["website_host_name"].(string), order.ID)
	msgRequest := map[string]any{
		"template_name": emailTemplate["send_to_provider"],
		"to":            provider.Contact.Email,
		"bcc":           []string{s.config["services_email"].(string)},
		"parameters":    parameters,
	}
	err = s.sqsNotification.Send(msgRequest)
	if err != nil {
		return err
	}
	// 3. update status of service order
	if err = s.etsDao.UpdateServiceOrder(map[string]any{"status": models.EtsOrderStatusSubmitted, "submitted_time": time.Now()}, order.ID); err != nil {
		return err
	}

	// 4. Auto assign staff
	if svc.ServiceType == "country_tourist" {
		if err := AutoAssignedOrderStaffToEtsOrder(int64(order.ID), s.etsDao); err != nil {
			return err
		}
	}

	if funk.ContainsString(notification.URGENT_PRODUCT_NAMES, svc.Name) {
		if err := notification.NotifyViaZaloForUrgentVisa(s.etsDao, cast.ToInt64(order.ID)); err != nil {
			return err
		}
	}

	if utils.StructToJSON(svc.Attributes).Get("form").String() == "" {
		if err = s.etsDao.UpdateServiceTaskStatusByOrderID(models.EtsTaskStatusConfirmed, int64(order.ID)); err != nil {
			return err
		}
	}

	return nil
}

func (s *SubmitDispatcher) HandleShipmentETS(order *models.ServiceOrder, content, carrier, serviceType, status string) error {
	userContact, err := shipment.GetContactOfUser(s.dao, order.ShipmentInfo.String)
	if err != nil {
		return err
	}

	var provider models.EtsProvider
	if err := s.dao.ORM().Select("id, name, address, contact").Table("ets_provider").Where("id = ?", order.ProviderID).First(&provider).Error; err != nil {
		return err
	}

	user, err := s.dao.GetUserByID(order.UserID)
	if err != nil {
		return err
	}

	var from, to = userContact, &fedex.ShipmentContact{
		City:    provider.Address.City,
		Name:    provider.Name,
		State:   provider.Address.State,
		Phone:   provider.Contact.Phone,
		Email:   provider.Contact.Email,
		Address: provider.Address.Address,
		ZipCode: provider.Address.ZipCode.String,
		Country: provider.Address.Country,
	}

	if content == "consulate_to_user" {
		from, to = to, from
	}

	ets, err := s.etsDao.QueryExtendedTravelServices(map[string]any{"id": order.ServiceID}, 0, 1)
	if err != nil {
		return err
	}

	data := shipment.CreateShipmentData{
		UserID:           order.UserID,
		UserEmail:        user.Email,
		SqsShip:          s.sqsShip,
		SqsNotification:  s.sqsNotification,
		ProductType:      product.ETSProductType,
		OrderID:          order.ID,
		ProductID:        order.ServiceID,
		ShippingContent:  content,
		Config:           s.config,
		ShipmentCarrier:  carrier,
		Status:           status,
		ServiceType:      serviceType,
		From:             from,
		To:               to,
		OrderServiceType: ets[0].ServiceType,
	}
	if err = shipment.CreateShipment(data); err != nil {
		return err
	}
	return nil
}

func (s *SubmitDispatcher) PassportServiceProcess(order *models.ServiceOrder) error {
	tasks, _, err := s.etsDao.QueryServiceTasks(map[string]any{"order_id": order.ID}, 0, 0)
	if err != nil {
		return err
	}
	svcs, err := s.etsDao.QueryExtendedTravelServices(map[string]any{"id": order.ServiceID}, 0, 1)
	if err != nil {
		return err
	}
	svc := svcs[0]
	attrJ := utils.StructToJSON(svc.Attributes)
	orderForms := []string{}
	taskForms := map[int64][]string{}
	for _, form := range attrJ.Get("form.order_forms").Array() {
		if attrJ.Get("form.form_detail").Get(form.String()).Get("auto_create").Bool() {
			orderForms = append(orderForms, form.String())
		}
	}

	for _, task := range tasks {
		if attrJ.Get("form").Exists() {
			for _, form := range attrJ.Get("form.task_forms").Array() {
				if attrJ.Get("form.form_detail").Get(form.String()).Get("auto_create").Bool() {
					taskForms[task.ID] = append(taskForms[task.ID], form.String())
				}
			}
		}
	}

	if order.AutoGeneratedFormAt == nil {
		if err := s.etsDao.UpdateServiceOrder(map[string]any{"auto_generated_form_at": time.Now()}, order.ID); err != nil {
			return err
		}
		for taskID, formNames := range taskForms {
			if len(formNames) > 0 {
				if err := s.etsDao.UpdateServiceTask(map[string]any{"status": models.EtsTaskStatusFormCreating}, taskID); err != nil {
					return err
				}
			} else {
				if err := s.etsDao.UpdateServiceTask(map[string]any{"status": models.EtsTaskStatusConfirmed}, taskID); err != nil {
					return err
				}
			}
		}
		if len(taskForms) > 0 || len(orderForms) > 0 {
			if err := applications.CallToGenerateETSForm(order.ID, orderForms, taskForms); err != nil {
				return err
			}
		}
	}

	if !IsAllFormGenerated(svc, order, tasks) {
		return nil
	}

	confJ := utils.StructToJSON(order.QueryPodValues)

	upload, okUpload := (*svc.Attributes)["upload"].(map[string]any)

	switch order.Status {
	case models.EtsOrderStatusPaid, models.EtsOrderStatusUnderReview:
		{

			attributeJ := utils.StructToJSON(svc.Attributes)
			numberOfLabels := 1
			if attributeJ.Get("separate_shipment").Bool() {
				numberOfLabels = len(tasks)
			}

			var updateStatus string
			if attributeJ.Get("receive_method").String() == "mail" {
				for i := 0; i < numberOfLabels; i++ {
					if shippingServiceID := attributeJ.Get("shipping_labels.consulate_to_user.shipping_service_id").Int(); shippingServiceID > 0 {
						shippingService, err := s.dao.GetShippingServiceByID(shippingServiceID)
						if err != nil {
							return err
						}

						if err = s.HandleShipmentETS(order, "consulate_to_user", shippingService.Carrier, shippingService.Service, models.EtsOrderStatusCreatedShipmentUser); err != nil {
							return err
						}
						updateStatus = models.EtsOrderStatusWaitingShipmentUser
					} else {
						updateStatus = models.EtsOrderStatusCreatedShipmentUser
					}
				}
			} else {
				updateStatus = models.EtsOrderStatusCreatedShipmentUser
			}

			if err = s.etsDao.UpdateServiceOrder(map[string]any{"status": updateStatus}, order.ID); err != nil {
				return err
			}

			utils.LogOrderEvent(utils.SUCCESS, "passport", order.ID, "ORDER_CHANGE_STATUS", fmt.Sprintf("Order change status from %s to %s", order.Status, updateStatus))
			return nil
		}
	case models.EtsOrderStatusCreatedShipmentUser:
		{
			/* check by process type
			- package: send email with instruction. The AD will mail the passport package to the user by manual.
			- auto: send email to user and gov.
			*/

			// 1. send to user
			user, err := s.dao.GetUserByID(order.UserID)
			if err != nil {
				return err
			}

			shipment, err := s.dao.GetVisaShipment(order.ShipmentInfo.String)
			if err != nil {
				return err
			}
			shipmentJ := utils.StructToJSON(shipment).Get("shipping_contact")

			fullName := "SIR/MADAM"
			if user.GivenName != "" {
				fullName = strings.ToUpper(fmt.Sprintf("%s %s", strings.TrimSpace(user.GivenName), strings.TrimSpace(user.Surname)))
			} else if shipmentJ.Get("given_name").String() != "" {
				fullName = strings.ToUpper(fmt.Sprintf("%s %s", strings.TrimSpace(shipmentJ.Get("given_name").String()), strings.TrimSpace(shipmentJ.Get("surname").String())))
			}

			data := map[string]any{
				"OrderID":           order.ID,
				"FullName":          strings.ToUpper(fullName),
				"Country":           s.localize.EN.Country[svc.Country],
				"ServiceType":       s.localize.EN.ServiceType[svc.ServiceType],
				"RegionOfResidence": s.localize.EN.Country[confJ.Get("service_core_info_region_of_residence").String()],
				"ProcessingTime":    s.localize.EN.ProcessingTime[attrJ.Get("processing_time").String()],
				"Task":              s.localize.EN.Tasks[strings.Join(svc.Tasks, ",")],
				"SubmitMethod":      s.localize.EN.SubmitMethod[attrJ.Get("submit_method").String()],
				"PickupMethod":      s.localize.EN.PickupMethod[attrJ.Get("receive_method").String()],
			}

			dataApplications := []map[string]any{}
			for _, task := range tasks {
				dataApplications = append(dataApplications, map[string]any{
					"FullName":          task.GetAppName(svc.ServiceType),
					"RegionOfResidence": s.localize.EN.Country[confJ.Get("service_core_info_region_of_residence").String()],
					"ProcessingTime":    s.localize.EN.ProcessingTime[attrJ.Get("processing_time").String()],
					"Task":              s.localize.EN.Tasks[strings.Join(svc.Tasks, ",")],
				})
			}
			data["Applications"] = dataApplications

			for _, v := range tasks[0].InputPods {
				if v.ID == "additional_question_core_info_passport_document" {
					if _, ok := v.Value.FE.(string); ok {
						data["PassportType"] = s.localize.EN.PassportType[v.Value.FE.(string)]
					} else {
						return fmt.Errorf("Order %v: missing value of the pods *********", order.ID)
					}
				}
			}
			if svc.Country == "USA" && svc.Tasks[0] == "renew" {
				data["ADOfficeLocation"] = "AriaDirect 6203 San Ignacio Ave suite 110 San Jose, CA 95119" // hard code for now
			}

			attachments := []map[string]any{}
			if attrJ.Get("process_type").String() == "auto" {
				if attrJ.Get("submit_method").String() == "user_in_person" {
					for key, v := range *order.OutputFiles {
						if key != "documents" {
							bucket, key, err := utils.UrlToS3BucketAndKey(v.(string))
							if err != nil {
								return err
							}
							attachments = append(attachments, map[string]any{
								"bucket":    bucket,
								"key":       key,
								"file_name": utils.GetFileNameFromURL(v.(string)),
							})
						}
					}
				}
				if attrJ.Get("submit_method").String() == "email" && okUpload {
					for k, v := range *order.OutputFiles {
						if utils.Contain(k, upload["forms"]) {
							bucket, key, err := utils.UrlToS3BucketAndKey(v.(string))
							if err != nil {
								return err
							}
							attachments = append(attachments, map[string]any{
								"bucket":    bucket,
								"key":       key,
								"file_name": s.localize.EN.Form[k],
							})
						}
					}
				}
				if strings.Contains(utils.StructToJSON(svc).Get("tag").String(), "form_filler") {
					for _, task := range tasks {
						bucket, key, err := utils.UrlToS3BucketAndKey(gjson.ParseBytes(task.FormCallback).Get("form").String())
						if err == nil {
							attachments = append(attachments, map[string]any{
								"bucket":    bucket,
								"key":       key,
								"file_name": slugify.Marshal(task.GetAppName(svc.ServiceType)) + ".pdf",
							})
						}

					}
				}
			}

			sendToUserTemplate := attrJ.Get("email_template.send_to_user").String()
			if sendToUserTemplate == "" {
				return fmt.Errorf("not found email template to send the user in product %d", order.ServiceID)
			}

			msg := map[string]any{
				"template_name": sendToUserTemplate,
				"to":            user.Email,
				"bcc":           []string{s.config["services_email"].(string)},
				"parameters":    data,
				"attachments":   attachments,
			}

			resp, err := s.etsDao.QueryServiceOrders(models.ServiceOrderFilter{
				ID:              []string{strconv.Itoa(order.ID)},
				IncludeShipment: true,
				Limit:           1,
			})
			if err != nil {
				return err
			}

			shipmentEmail := utils.StructToJSON(resp.Data[0]).Get("shipment.shipping_contact.email").String()
			if shipmentEmail != "" && shipmentEmail != user.Email {
				msg["cc"] = []string{shipmentEmail}
			}

			if err = s.sqsNotification.Send(msg); err != nil {
				return err
			}

			// 2. send to gov
			if attrJ.Get("submit_method").String() == "email" {
				// 2.1. Make message
				message := map[string]any{
					"product_type": product.ETSProductType,
					"id":           order.ID,
					"method":       NotificationMethod,
					"callback":     fmt.Sprintf("https://%s/ets-internal/%d/update-status", s.config["package_host_name"].(string), order.ID),
				}
				// 2.2. Send package_id and necessary payload to the submit sqs
				err := s.sqsSubmit.Send(message)
				if err != nil {
					log.Error().Str("error", err.Error()).Int("order_id", order.ID).Msg("failed to send to submit queue")
					return err
				}
			}

			// 3. send ets request email to provider
			provider, err := s.etsDao.GetEtsProviderByID(order.ProviderID)
			if err != nil {
				return err
			}
			if s.config["env"].(string) != ProductionEnv {
				provider.Contact.Email = s.config["services_email"].(string)
			}

			sendToProviderTemplate := attrJ.Get("email_template.send_to_provider").String()
			if sendToProviderTemplate == "" {
				fmt.Printf("not found email template to send the provider in product %d", order.ServiceID)
			} else {
				data["ProviderName"] = provider.Name
				data["URL"] = fmt.Sprintf("%s/dashboard/orders/detail?order_id=%d&service=ets", s.config["website_host_name"].(string), order.ID)

				msgRequest := map[string]any{
					"template_name": sendToProviderTemplate,
					"to":            provider.Contact.Email,
					"bcc":           []string{s.config["services_email"].(string)},
					"parameters":    data,
				}
				if err = s.sqsNotification.Send(msgRequest); err != nil {
					return err
				}

			}

			// 4. update package
			up := map[string]any{}
			var status string
			switch attrJ.Get("process_type").String() {
			case "package":
				{
					status = models.EtsOrderStatusSubmitted
				}
			case "auto":
				{
					if attrJ.Get("submit_method").String() == "user_in_person" {
						status = models.EtsOrderStatusSubmitted
					} else if strings.Contains(utils.StructToJSON(svc).Get("tag").String(), "form_filler") {
						status = models.EtsOrderStatusCompleted
						// TODO: Update task to complete
					} else {
						status = models.EtsOrderStatusSubmitted
					}
				}
			}
			up["submitted_time"] = time.Now()
			up["status"] = status
			err = s.etsDao.UpdateServiceOrder(up, order.ID)
			if err != nil {
				return err
			}
			return nil
		}
	}
	return nil
}

func IsAllFormGenerated(svc *models.ExtendedTravelService, order *models.ServiceOrder, tasks []*models.ServiceTask) bool {
	attrJ := utils.StructToJSON(svc.Attributes)
	orderForms := attrJ.Get("form.order_forms").Array()
	taskForms := attrJ.Get("form.task_forms").Array()

	for _, formName := range orderForms {
		if attrJ.Get("form.form_detail").Get(formName.String()).Get("auto_create").Bool() && !utils.StructToJSON(order.FormCallback).Get(formName.String()).Get("success").Bool() {
			return false
		}
	}
	for _, task := range tasks {
		for _, formName := range taskForms {
			if attrJ.Get("form.form_detail").Get(formName.String()).Get("auto_create").Bool() && !utils.StructToJSON(task.FormCallback).Get(formName.String()).Get("success").Bool() {
				return false
			}
		}
	}
	return true
}
