package main

import (
	"encoding/json"
	"errors"
	"fmt"
	"os"

	"github.com/aws/aws-sdk-go/aws/session"
	"github.com/nlopes/slack"
	"github.com/robfig/cron"
	"github.com/rs/zerolog/log"
	"github.com/urfave/cli"

	"bitbucket.org/persistence17/aria/golang_services/sdk/db"
	"bitbucket.org/persistence17/aria/golang_services/sdk/fedex"
	"bitbucket.org/persistence17/aria/golang_services/sdk/utils"
	"github.com/aws/aws-sdk-go/aws"
)

func watchDogAction() cli.ActionFunc {
	return func(c *cli.Context) error {
		wd, err := NewWatchDogService(c)
		if err != nil {
			return err
		}

		wd.CheckAndSendUpdatedFastlaneOrder(0)
		var tasks []*utils.Task
		// Every 1 hour
		tasks = append(tasks, utils.NewTask(func() error {
			var cr = cron.New()

			if err := cr.AddFunc("@every 1h", func() {
				if !wd.config.IsRunning {
					return
				}
				if err := wd.CheckPaymentPendingTooLong(); err != nil {
					log.Error().Msgf("Task CheckPaymentPendingTooLong return error %v", err)
				}

				if err := wd.SentNotificationToUserWhenNoLogin(); err != nil {
					log.Error().Msgf("Task SentNotificationToUserWhenNoLogin return error %v", err)
				}

				// Confirm with Cindy on 2022-01-04 16:29 we should disable it
				// if err := wd.LockUserAccountWhenNoLogin(); err != nil {
				// 	log.Error().Msgf("Task LockUserAccountWhenNoLogin return error %v", err)
				// }

				if err := wd.CheckIncompleteETSOrder(); err != nil {
					log.Error().Msgf("Task CheckIncompleteETSOrder return error %v", err)
				}

				// if err := wd.DeleteIncompleteETSOrder(); err != nil {
				// 	log.Error().Msgf("Task DeleteIncompleteETSOrder return error %v", err)
				// }

				// if err := wd.CheckUnderReviewXDaysETSOrder(); err != nil {
				// 	log.Error().Msgf("Task CheckUnderReviewXDaysETSOrder return error %v", err)
				// }

				// if err := wd.SendFeedbackCompleteOrder(); err != nil {
				// 	log.Error().Msgf("Task SendFeedbackCompleteOrder return error %v", err)
				// }

			}); err != nil {
				return err
			}

			cr.Start()
			return nil
		}))

		// Every 1 hour
		tasks = append(tasks, utils.NewTask(func() error {
			var cr = cron.New()

			if err := cr.AddFunc("@every 1h", func() {
				if !wd.config.IsRunning {
					return
				}
			}); err != nil {
				return err
			}

			cr.Start()
			return nil
		}))

		// Every 1 hour
		tasks = append(tasks, utils.NewTask(func() error {
			var cr = cron.New()
			if err := cr.AddFunc("@every 1h", func() {
				if !wd.config.IsRunning {
					return
				}
				// if err := wd.TrackDeliveryStatus(); err != nil {
				// 	log.Error().Msgf("Shipment: Task return error %v", err)
				// }
			}); err != nil {
				return err
			}
			cr.Start()
			return nil
		}))

		// Every 1 hour
		tasks = append(tasks, utils.NewTask(func() error {
			var cr = cron.New()
			if err := cr.AddFunc("@every 1h", func() {
				if !wd.config.IsRunning {
					return
				}
				// if err := wd.WarningExpiredPassport(); err != nil {
				// 	log.Error().Msgf("Task WarningExpiredPassport return error %v", err)
				// }
				// if err := wd.WarningExpiredVisa(); err != nil {
				// 	log.Error().Msgf("Task WarningExpiredVisa return error %v", err)
				// }
			}); err != nil {
				return err
			}
			cr.Start()
			return nil
		}))

		// Every 1 hour check reminder
		tasks = append(tasks, utils.NewTask(func() error {
			var cr = cron.New()
			if err := cr.AddFunc("@every 1h", func() {
				if !wd.config.IsRunning {
					return
				}
				// if err := wd.RemindProcessingPackage(); err != nil {
				// 	log.Error().Msgf("Task RemindProcessingPackage return error %v", err)
				// }
				// if err := wd.RemindProcessingETSOrder(); err != nil {
				// 	log.Error().Msgf("Task RemindProcessingETSOrder return error %v", err)
				// }
				// if err := wd.RemindProcessingETSOrderFastlane3Days(); err != nil {
				// 	log.Error().Msgf("Task RemindProcessingETSOrderFastlane3Days return error %v", err)
				// }
				// if err := wd.RemindProcessingETSOrderFastlane1Day(); err != nil {
				// 	log.Error().Msgf("Task RemindProcessingETSOrderFastlane1Day return error %v", err)
				// }
				if err := wd.VNMPassportOrderDelivery(); err != nil {
					log.Error().Msgf("VNMPassportOrderDelivery: Task return error %v", err)
				}
			}); err != nil {
				return err
			}
			cr.Start()
			return nil
		}))

		// Every 5 minutes
		tasks = append(tasks, utils.NewTask(func() error {
			var cr = cron.New()
			if err := cr.AddFunc("@every 5m", func() {
				if !wd.config.IsRunning {
					return
				}
				if err := wd.SendRemindToFastlaneOrderBeforeXHours(); err != nil {
					log.Error().Msgf("Task SendRemindToFastlaneOrderBeforeXHours return error %v", err)
				}

				// if err := wd.SendRemindToFastlaneOrderBeforeXHoursInWhatsApp(); err != nil {
				// 	log.Error().Msgf("Task SendRemindToFastlaneOrderBeforeXHoursInWhatsApp return error %v", err)
				// }

				// if err := wd.SendRemindWhenCreateFastlaneOrder(); err != nil {
				// 	log.Error().Msgf("Task SendRemindWhenCreateFastlaneOrder return error %v", err)
				// }
				if err := wd.SendRemindToUrgentEVisa(); err != nil {
					log.Error().Msgf("Task SendRemindToUrgentEVisa return error %v", err)
				}

			}); err != nil {
				return err
			}
			cr.Start()
			return nil
		}))

		// Every 5 minutes
		tasks = append(tasks, utils.NewTask(func() error {
			var cr = cron.New()
			if err := cr.AddFunc("@every 5m", func() {
				if !wd.config.IsRunning {
					return
				}
				if err := wd.SendRemindToFastlaneOrderJoinWhatsApp(); err != nil {
					log.Error().Msgf("Task SendRemindToFastlaneOrderJoinWhatsApp return error %v", err)
				}
			}); err != nil {
				return err
			}
			cr.Start()
			return nil
		}))

		// Every 10 seconds
		tasks = append(tasks, utils.NewTask(func() error {
			var cr = cron.New()
			if err := cr.AddFunc("@every 10s", func() {
				if !wd.config.IsRunning {
					return
				}
				if err := wd.ProcessJob(); err != nil {
					log.Error().Msgf("Task ProcessJob return error %v", err)
				}
			}); err != nil {
				return err
			}
			cr.Start()
			return nil
		}))

		// Runs every 3h
		tasks = append(tasks, utils.NewTask(func() error {
			var cr = cron.New()
			if err := cr.AddFunc("@every 3h", func() {
				if !wd.config.IsRunning {
					return
				}
				fmt.Sprintln("CheckAndGetEVisaForm")
				if err := wd.CheckAndGetEVisaForm(); err != nil {
					log.Error().Msgf("Task CheckAndGetEVisaForm return error %v", err)
				}
			}); err != nil {
				return err
			}
			cr.Start()
			return nil
		}))

		tasks = append(tasks, utils.NewTask(func() error {
			select {} // Wait forever
		}))

		var p = utils.NewPool(tasks, 10) // Set max concurrency
		p.Run()

		if p.HasErrors() {
			for _, task := range p.Tasks {
				if task.Err != nil {
					log.Error().Msgf("Task return error %v", task.Err)
				}
			}
			return fmt.Errorf("Pool error running, please check log! ")
		}
		return nil

	}
}

// NewWatchDogService make new watchdog instant
func NewWatchDogService(c *cli.Context) (*WatchDogInstant, error) {
	dbConfigMap := utils.GetMapEnv("ad_db")
	if dbConfigMap["write_host"].(string) == "" {
		return nil, fmt.Errorf("missing write_host in ad_db")
	}
	if dbConfigMap["dbname"].(string) == "" {
		return nil, fmt.Errorf("missing dbname in ad_db")
	}
	if dbConfigMap["username"].(string) == "" {
		return nil, fmt.Errorf("missing username in ad_db")
	}
	if dbConfigMap["password"].(string) == "" {
		return nil, fmt.Errorf("missing password in ad_db")
	}

	// Database connection
	conn, err := db.NewAuroraDBFromConfigMap(dbConfigMap)
	if err != nil {
		return nil, err
	}

	// Service configuration
	svConfStr := os.Getenv(c.String("service-config"))
	var serviceConfig WatchDogConfig
	if err := json.Unmarshal([]byte(svConfStr), &serviceConfig); err != nil {
		return nil, err
	}

	// Hostname
	websiteConfigMap := utils.GetMapEnv("ad_website")
	if val, ok := websiteConfigMap["host_name"].(string); ok && val != "" {
		serviceConfig.HostName = val
	} else {
		return nil, fmt.Errorf("missing host_name in ad_website")
	}

	secret := utils.GetMapEnv("ad_secrets")
	if val, ok := secret["jwt"].(string); ok && val != "" {
		serviceConfig.SecretKey = val
	} else {
		return nil, fmt.Errorf("missing jwt in ad_secrets")
	}

	// Email configuration
	emailConfigMap := utils.GetMapEnv("ad_email")
	if emailConfigMap["support"].(string) == "" {
		return nil, fmt.Errorf("missing support in ad_email")
	}

	serviceConfig.EmailConfig = map[string]string{
		"support": emailConfigMap["support"].(string),
	}

	// Default is running
	serviceConfig.IsRunning = true

	// SQS config
	sqsConfStr := os.Getenv(c.String("sqs-config"))
	var sqsConfMap map[string]any
	if err := json.Unmarshal([]byte(sqsConfStr), &sqsConfMap); err != nil {
		return nil, err
	}

	if sqsConfMap["url_prefix"].(string) == "" {
		return nil, fmt.Errorf("missing url_prefix in sqs-config")
	}

	if sqsConfMap["notification_sqs_name"].(string) == "" {
		return nil, fmt.Errorf("missing notification_user_sqs_name in sqs-config")
	}

	notifyURL := fmt.Sprintf("%s/%s", sqsConfMap["url_prefix"].(string), sqsConfMap["notification_sqs_name"].(string))
	if notifyURL == "" {
		return nil, errors.New("missing notification_user_sqs_name")
	}

	awsConf := utils.ParseJsonObjStr(c.String("ad-aws"))
	awsConfig := aws.NewConfig().WithRegion(awsConf["region"].(string)).WithLogLevel(aws.LogOff)
	sess, err := session.NewSession(awsConfig)
	if err != nil {
		return nil, err
	}

	serviceConfig.BucketNames = utils.GetMapEnv(c.String("ad-s3"))
	log.Info().Interface("bucket_config", serviceConfig.BucketNames).Msg("S3 bucket config from ENV")

	// Fedex service
	fdx, err := fedex.NewFedex()
	if err != nil {
		return nil, err
	}

	return &WatchDogInstant{
		dao:        db.NewDaoWithDb(conn),
		etsDao:     db.NewEtsDao(conn),
		slack:      slack.New(serviceConfig.SlackAPIKey),
		config:     serviceConfig,
		awsSession: sess,
		notifyURL:  notifyURL,
		fedex:      fdx,
	}, nil
}
