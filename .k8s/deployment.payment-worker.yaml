apiVersion: apps/v1
kind: Deployment
metadata:
  name: payment-worker-service
  labels:
    app: payment-worker-service
    env: config-map
spec:
  replicas: 1
  selector:
    matchLabels:
      app: payment-worker-service
  template:
    metadata:
      labels:
        app: payment-worker-service
    spec:
      imagePullSecrets:
        - name: regcred
      containers:
        - name: payment-worker-service
          image: 853431205376.dkr.ecr.us-west-2.amazonaws.com/payment-worker-service:55e20b43e
          command: ["./payment_worker"]
          args:
            [
              "svc",
              "--db-config",
              "ad_db",
              "--use-wechat-pay-prod",
              "--use-authorize-prod",
              "--conf-file",
              "conf/payments.yaml",
              "--ad_authorize",
              "ad_authorize",
              "--ad_wechatpay",
              "ad_wechatpay",
              "--ad_onepay",
              "ad_onepay",
            ]
          imagePullPolicy: IfNotPresent
          envFrom:
            - configMapRef:
                name: app-environment
