apiVersion: apps/v1
kind: Deployment
metadata:
  name: email-temp-service
  labels:
    app: email-temp-service
    env: config-map
spec:
  replicas: 1
  selector:
    matchLabels:
      app: email-temp-service
  template:
    metadata:
      labels:
        app: email-temp-service
    spec:
      imagePullSecrets:
        - name: regcred
      containers:
        - name: email-temp-service
          image: 853431205376.dkr.ecr.us-west-2.amazonaws.com/email-temp-service:b6f6c7c9dZ250305T094906
          command: ["./email-temp"]
          args:
            [
              "svc",
            ]
          imagePullPolicy: IfNotPresent
          ports:
            - containerPort: 3000
          envFrom:
            - configMapRef:
                name: app-environment
---
apiVersion: v1
kind: Service
metadata:
  name: email-temp-service
  labels:
    app: email-temp-service
spec:
  type: ClusterIP
  ports:
    - port: 3000
      targetPort: 3000
      protocol: TCP
  selector:
    app: email-temp-service
