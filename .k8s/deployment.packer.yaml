apiVersion: apps/v1
kind: Deployment
metadata:
  name: packer-service
  labels:
    app: packer-service
    env: config-map
spec:
  replicas: 1
  selector:
    matchLabels:
      app: packer-service
  template:
    metadata:
      labels:
        app: packer-service
    spec:
      imagePullSecrets:
        - name: regcred
      containers:
        - name: packer-service
          image: ************.dkr.ecr.us-west-2.amazonaws.com/packer-service:56a837948Z250520T180804
          command: ["./packer"]
          args:
            [
              "p",
              "--ad_db",
              "ad_db",
              "--ad_aws",
              "ad_aws",
              "--ad_sqs",
              "ad_sqs",
              "--ad_s3",
              "ad_s3",
              "--ad_packer_service",
              "ad_packer_service",
              "--ad_user_account_service",
              "ad_user_account_service",
              "--ad_secrets",
              "ad_secrets",
              "--ad_api_token",
              "ad_api_token",
            ]
          imagePullPolicy: IfNotPresent
          ports:
            - containerPort: 3000
          envFrom:
            - configMapRef:
                name: app-environment
---
apiVersion: v1
kind: Service
metadata:
  name: packer-service
  labels:
    app: packer-service
spec:
  type: ClusterIP
  ports:
    - port: 3000
      targetPort: 3000
      protocol: TCP
  selector:
    app: packer-service
