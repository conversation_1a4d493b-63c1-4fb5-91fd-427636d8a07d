apiVersion: apps/v1
kind: Deployment
metadata:
  name: packer-online-captcha-service
  labels:
    app: packer-online-captcha-service
    env: config-map
spec:
  replicas: 0
  selector:
    matchLabels:
      app: packer-online-captcha-service
  template:
    metadata:
      labels:
        app: packer-online-captcha-service
    spec:
      imagePullSecrets:
        - name: regcred
      containers:
        - name: packer-online-captcha-service
          image: 853431205376.dkr.ecr.us-west-2.amazonaws.com/packer-online-captcha-service:c67699463
          command: ["npm"]
          args: ["run", "start"]
          imagePullPolicy: IfNotPresent
          envFrom:
            - configMapRef:
                name: app-environment
