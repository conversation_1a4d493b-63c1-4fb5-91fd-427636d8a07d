apiVersion: apps/v1
kind: Deployment
metadata:
  name: user-management-service
  labels:
    app: user-management-service
    env: config-map
spec:
  replicas: 1
  selector:
    matchLabels:
      app: user-management-service
  template:
    metadata:
      labels:
        app: user-management-service
    spec:
      imagePullSecrets:
        - name: regcred
      containers:
        - name: user-management-service
          image: 853431205376.dkr.ecr.us-west-2.amazonaws.com/user-management-service:1d690b9f9Z250530T135956
          command: ["./user-authentication"]
          args: []
          imagePullPolicy: IfNotPresent
          ports:
            - containerPort: 3000
          envFrom:
            - configMapRef:
                name: app-environment
---
apiVersion: v1
kind: Service
metadata:
  name: user-management-service
  labels:
    app: user-management-service
spec:
  type: ClusterIP
  ports:
    - port: 3000
      targetPort: 3000
      protocol: TCP
  selector:
    app: user-management-service
