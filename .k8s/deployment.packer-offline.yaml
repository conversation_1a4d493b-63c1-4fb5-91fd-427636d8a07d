apiVersion: apps/v1
kind: Deployment
metadata:
  name: packer-offline-service
  labels:
    app: packer-offline-service
    env: config-map
spec:
  replicas: 0
  selector:
    matchLabels:
      app: packer-offline-service
  template:
    metadata:
      labels:
        app: packer-offline-service
    spec:
      imagePullSecrets:
        - name: regcred
      containers:
        - name: packer-offline-service
          image: 853431205376.dkr.ecr.us-west-2.amazonaws.com/packer-offline-service:41ffbb142Z240719T143132
          command: ["npm"]
          args: ["run", "start"]
          imagePullPolicy: IfNotPresent
          envFrom:
            - configMapRef:
                name: app-environment
