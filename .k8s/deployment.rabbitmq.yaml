apiVersion: v1
kind: ConfigMap
metadata:
  name: rabbitmq-config
data:
  rabbitmq.conf: |
    default_user = admin
    default_pass = RABBITMQAD2025
    listeners.tcp.default = 5672
    management.listener.port = 15672
    vm_memory_high_watermark.relative = 0.8
    disk_free_limit.absolute = 1GB
    cluster_partition_handling = autoheal
    management.load_definitions = /etc/rabbitmq/definitions.json

  definitions.json: |
    {
      "users": [
        {"name": "admin", "password": "RABBITMQAD2025", "tags": "administrator"},
        {"name": "app_user", "password": "app_password", "tags": ""}
      ],
      "vhosts": [
        {"name": "/"},
        {"name": "app_vhost"}
      ],
      "permissions": [
        {"user": "admin", "vhost": "/", "configure": ".*", "write": ".*", "read": ".*"},
        {"user": "app_user", "vhost": "app_vhost", "configure": ".*", "write": ".*", "read": ".*"}
      ],
      "queues": [
        {
          "name": "default_queue",
          "vhost": "app_vhost",
          "durable": true,
          "auto_delete": false,
          "arguments": {
            "x-queue-type": "classic",
            "x-max-length": 10000,
            "x-overflow": "reject-publish"
          }
        }
      ],
      "exchanges": [
        {
          "name": "default_exchange",
          "vhost": "app_vhost",
          "type": "direct",
          "durable": true,
          "auto_delete": false,
          "internal": false,
          "arguments": {}
        }
      ],
      "bindings": [
        {
          "source": "default_exchange",
          "vhost": "app_vhost",
          "destination": "default_queue",
          "destination_type": "queue",
          "routing_key": "default_key",
          "arguments": {}
        }
      ],
      "policies": [
        {
          "vhost": "app_vhost",
          "name": "ha-policy",
          "pattern": ".*",
          "apply-to": "queues",
          "definition": {
            "ha-mode": "all",
            "ha-sync-mode": "automatic"
          },
          "priority": 1
        }
      ]
    }
---
apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: rabbitmq
spec:
  serviceName: rabbitmq
  replicas: 1
  selector:
    matchLabels:
      app: rabbitmq
  template:
    metadata:
      labels:
        app: rabbitmq
    spec:
      containers:
      - name: rabbitmq
        image: rabbitmq:3.11-management
        ports:
        - containerPort: 5672
          name: amqp
        - containerPort: 15672
          name: management
        env:
        - name: RABBITMQ_ERLANG_COOKIE
          value: "RABBITMQCOOKIE"
        volumeMounts:
        - name: config-volume
          mountPath: /etc/rabbitmq/rabbitmq.conf
          subPath: rabbitmq.conf
        - name: definitions-volume
          mountPath: /etc/rabbitmq/definitions.json
          subPath: definitions.json
        - name: data
          mountPath: /var/lib/rabbitmq
        resources:
          requests:
            memory: "512Mi"
            cpu: "200m"
          limits:
            memory: "2Gi"
            cpu: "500m"
        livenessProbe:
          exec:
            command: ["rabbitmq-diagnostics", "status"]
          initialDelaySeconds: 120
          periodSeconds: 60
          timeoutSeconds: 15
        readinessProbe:
          exec:
            command: ["rabbitmq-diagnostics", "check_port_connectivity"]
          initialDelaySeconds: 60
          periodSeconds: 10
          timeoutSeconds: 10
      volumes:
      - name: config-volume
        configMap:
          name: rabbitmq-config
          items:
          - key: rabbitmq.conf
            path: rabbitmq.conf
      - name: definitions-volume
        configMap:
          name: rabbitmq-config
          items:
          - key: definitions.json
            path: definitions.json
  volumeClaimTemplates:
  - metadata:
      name: data
    spec:
      accessModes: ["ReadWriteOnce"]
      resources:
        requests:
          storage: 1Gi
---
apiVersion: v1
kind: Service
metadata:
  name: rabbitmq-management
  labels:
    app: rabbitmq
spec:
  selector:
    app: rabbitmq
  ports:
  - port: 5672
    name: amqp
    targetPort: 5672
  - port: 15672
    name: management
    targetPort: 15672
  type: LoadBalancer