apiVersion: apps/v1
kind: Deployment
metadata:
  name: logger-service
  labels:
    app: logger-service
    env: config-map
spec:
  replicas: 1
  selector:
    matchLabels:
      app: logger-service
  template:
    metadata:
      labels:
        app: logger-service
    spec:
      imagePullSecrets:
        - name: regcred
      containers:
        - name: logger-service
          image: 853431205376.dkr.ecr.us-west-2.amazonaws.com/logger-service:271bb3235
          command: ["./logger"]
          args: ["svc", "--db-config", "ad_db"]
          imagePullPolicy: IfNotPresent
          ports:
            - containerPort: 3000
          envFrom:
            - configMapRef:
                name: app-environment
---
apiVersion: v1
kind: Service
metadata:
  name: logger-service
  labels:
    app: logger-service
spec:
  type: ClusterIP
  ports:
    - port: 3000
      targetPort: 3000
      protocol: TCP
  selector:
    app: logger-service
