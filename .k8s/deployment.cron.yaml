apiVersion: apps/v1
kind: Deployment
metadata:
  name: cron-service
  labels:
    app: cron-service
    env: config-map
spec:
  replicas: 1
  selector:
    matchLabels:
      app: cron-service
  template:
    metadata:
      labels:
        app: cron-service
    spec:
      imagePullSecrets:
        - name: regcred
      containers:
        - name: cron-service
          image: 853431205376.dkr.ecr.us-west-2.amazonaws.com/cron-service:b6f6c7c9dZ250223T094115
          command: ["./cron"]
          args: [ ]
          imagePullPolicy: IfNotPresent
          ports:
            - containerPort: 3000
          envFrom:
            - configMapRef:
                name: app-environment
---
apiVersion: v1
kind: Service
metadata:
  name: cron-service
  labels:
    app: cron-service
spec:
  type: ClusterIP
  ports:
    - port: 3000
      targetPort: 3000
      protocol: TCP
  selector:
    app: cron-service
