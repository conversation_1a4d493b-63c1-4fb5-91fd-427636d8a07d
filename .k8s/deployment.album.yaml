apiVersion: apps/v1
kind: Deployment
metadata:
  name: album-service
  labels:
    app: album-service
    env: config-map
spec:
  replicas: 1
  selector:
    matchLabels:
      app: album-service
  template:
    metadata:
      labels:
        app: album-service
    spec:
      imagePullSecrets:
        - name: regcred
      containers:
        - name: album-service
          image: ************.dkr.ecr.us-west-2.amazonaws.com/album-service:0ab6b1ced
          command: ["./album-service"]
          args:
            [
              "svc",
              "--db-config",
              "ad_db",
              "--account-service-config",
              "ad_user_account_service",
              "--ad_secrets",
              "ad_secrets",
              "--s3-bucket-config",
              "ad_s3",
              "--ad_aws",
              "ad_aws",
            ]
          imagePullPolicy: IfNotPresent
          ports:
            - containerPort: 3000
          envFrom:
            - configMapRef:
                name: app-environment
---
apiVersion: v1
kind: Service
metadata:
  name: album-service
  labels:
    app: album-service
spec:
  type: ClusterIP
  ports:
    - port: 3000
      targetPort: 3000
      protocol: TCP
  selector:
    app: album-service
