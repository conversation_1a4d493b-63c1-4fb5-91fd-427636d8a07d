apiVersion: apps/v1
kind: Deployment
metadata:
  name: id-reader-service
  labels:
    app: id-reader-service
    env: config-map
spec:
  replicas: 0
  selector:
    matchLabels:
      app: id-reader-service
  template:
    metadata:
      labels:
        app: id-reader-service
    spec:
      imagePullSecrets:
        - name: regcred
      containers:
        - name: id-reader-service
          image: 853431205376.dkr.ecr.us-west-2.amazonaws.com/id-reader-service:87d7eda05
          command: ["flask"]
          args: ["run", "-p", "3000", "-h", "0.0.0.0"]
          imagePullPolicy: IfNotPresent
          ports:
            - containerPort: 3000
          envFrom:
            - configMapRef:
                name: app-environment
---
apiVersion: v1
kind: Service
metadata:
  name: id-reader-service
  labels:
    app: id-reader-service
spec:
  type: ClusterIP
  ports:
    - port: 3000
      targetPort: 3000
      protocol: TCP
  selector:
    app: id-reader-service
