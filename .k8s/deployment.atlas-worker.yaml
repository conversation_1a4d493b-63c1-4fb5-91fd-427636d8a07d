apiVersion: apps/v1
kind: Deployment
metadata:
  name: atlas-worker-service
  labels:
    app: atlas-worker-service
    env: config-map
spec:
  replicas: 0
  selector:
    matchLabels:
      app: atlas-worker-service
  template:
    metadata:
      labels:
        app: atlas-worker-service
    spec:
      imagePullSecrets:
        - name: awsecr-cred
      containers:
        - name: atlas-worker-service
          image: 853431205376.dkr.ecr.us-west-2.amazonaws.com/atlas-worker-service:f933b5126
          command: ["python"]
          args: ["atlas.py"]
          imagePullPolicy: IfNotPresent
          envFrom:
            - configMapRef:
                name: app-environment
