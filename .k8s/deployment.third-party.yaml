apiVersion: apps/v1
kind: Deployment
metadata:
  name: third-party-service
  labels:
    app: third-party-service
    env: config-map
spec:
  replicas: 1
  selector:
    matchLabels:
      app: third-party-service
  template:
    metadata:
      labels:
        app: third-party-service
    spec:
      imagePullSecrets:
        - name: regcred
      containers:
        - name: third-party-service
          image: 853431205376.dkr.ecr.us-west-2.amazonaws.com/third-party-service:9f7390d1cZ241118T144105
          command: ["npm"]
          args: ["run", "start"]
          imagePullPolicy: IfNotPresent
          ports:
            - containerPort: 3000
          envFrom:
            - configMapRef:
                name: app-environment
---
apiVersion: v1
kind: Service
metadata:
  name: third-party-service
  labels:
    app: third-party-service
spec:
  type: ClusterIP
  ports:
    - port: 3000
      targetPort: 3000
      protocol: TCP
  selector:
    app: third-party-service
