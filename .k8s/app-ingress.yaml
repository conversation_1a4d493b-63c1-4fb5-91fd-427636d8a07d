apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: package-service
  annotations:
    kubernetes.io/ingress.class: nginx
    nginx.ingress.kubernetes.io/rewrite-target: "/$1"
    nginx.ingress.kubernetes.io/enable-cors: "false"
    nginx.ingress.kubernetes.io/proxy-body-size: 100m
spec:
  ingressClassName: nginx
  rules:
    - host: "api.ariadirectcorp.com"
      http:
        paths:
          - path: /(v1/atlas.*)
            pathType: Prefix
            backend:
              service:
                name: atlas-service
                port:
                  number: 3000
          - path: /(v1/pkg.*)
            pathType: Prefix
            backend:
              service:
                name: package-service
                port:
                  number: 3000
          - path: /(v1/packer.*)
            pathType: Prefix
            backend:
              service:
                name: packer-service
                port:
                  number: 3000
          - path: /(v1/payment.*)
            pathType: Prefix
            backend:
              service:
                name: payment-service
                port:
                  number: 3000
          - path: /(v1/ums.*)
            pathType: Prefix
            backend:
              service:
                name: user-management-service
                port:
                  number: 3000
          - path: /(v1/ad-app-versions.*)
            pathType: Prefix
            backend:
              service:
                name: version-service
                port:
                  number: 3000
          - path: /(v1/master-data.*)
            pathType: Prefix
            backend:
              service:
                name: master-data-service
                port:
                  number: 3000
          - path: /(v1/shipment.*)
            pathType: Prefix
            backend:
              service:
                name: shipment-service
                port:
                  number: 3000
          - path: /(v1/mrz-parser.*)
            pathType: Prefix
            backend:
              service:
                name: mrz-parser-service
                port:
                  number: 3000
          - path: /(v1/passport-photo.*)
            pathType: Prefix
            backend:
              service:
                name: passport-photo-service
                port:
                  number: 3000
          - path: /(v1/helper.*)
            pathType: Prefix
            backend:
              service:
                name: helper-service
                port:
                  number: 3000
          - path: /(v1/device_tokens.*)
            pathType: Prefix
            backend:
              service:
                name: device-service
                port:
                  number: 3000
          - path: /(v1/album-photo.*)
            pathType: Prefix
            backend:
              service:
                name: album-service
                port:
                  number: 3000
          - path: /(v1/third-party.*)
            pathType: Prefix
            backend:
              service:
                name: third-party-service
                port:
                  number: 3000
          - path: /(v1/logger.*)
            pathType: Prefix
            backend:
              service:
                name: logger-service
                port:
                  number: 3000
          - path: /(v1/command.*)
            pathType: Prefix
            backend:
              service:
                name: python-tool-service
                port:
                  number: 3000
          - path: /(v1/id-reader.*)
            pathType: Prefix
            backend:
              service:
                name: id-reader-service
                port:
                  number: 3000
          - path: /(v1/docx-to-pdf.*)
            pathType: Prefix
            backend:
              service:
                name: docx-to-pdf-service
                port:
                  number: 3000
          - path: /(v1/google-sheet.*)
            pathType: Prefix
            backend:
              service:
                name: google-sheet-service
                port:
                  number: 3000
          - path: /(v1/email-temp.*)
            pathType: Prefix
            backend:
              service:
                name: email-temp-service
                port:
                  number: 3000
    - host: "upload.ariadirectcorp.com"
      http:
        paths:
          - path: /(.*)
            pathType: Prefix
            backend:
              service:
                name: uploader-service
                port:
                  number: 3000