apiVersion: apps/v1
kind: Deployment
metadata:
  name: payment-service
  labels:
    app: payment-service
    env: config-map
spec:
  replicas: 1
  selector:
    matchLabels:
      app: payment-service
  template:
    metadata:
      labels:
        app: payment-service
    spec:
      imagePullSecrets:
        - name: regcred
      containers:
        - name: payment-service
          image: ************.dkr.ecr.us-west-2.amazonaws.com/payment-service:ac9984fdbZ250519T144950
          command: ["./payments"]
          args:
            [
              "svc",
              "--db-config",
              "ad_db",
              "--use-wechat-pay-prod",
              "--conf-file",
              "conf/payments.yaml",
              "--service-config",
              "ad_payment_service",
              "--account-service-config",
              "ad_user_account_service",
              "--ad_secrets",
              "ad_secrets",
              "--ad_website",
              "ad_website",
              "--ad_authorize",
              "ad_authorize",
              "--ad_wechatpay",
              "ad_wechatpay",
              "--ad_onepay",
              "ad_onepay",
              "--ad_paypal",
              "ad_paypal",
              "--ad_zellepay",
              "ad_zellepay",
              "--ad_stripe",
              "ad_stripe",
              "--ad_email",
              "ad_email",
              "--sqs-config",
              "ad_sqs",
              "--s3-region",
              "us-west-2",
              "--version-file-bucket",
              "ad-app-version",
              "--currency-config-file",
              "/app-payment-config/payment-config-by-currency.json",
              "--region-payment-config-file",
              "/app-payment-config/default-curency-by-country.json",
            ]
          imagePullPolicy: IfNotPresent
          ports:
            - containerPort: 3000
          envFrom:
            - configMapRef:
                name: app-environment
---
apiVersion: v1
kind: Service
metadata:
  name: payment-service
  labels:
    app: payment-service
spec:
  type: ClusterIP
  ports:
    - port: 3000
      targetPort: 3000
      protocol: TCP
  selector:
    app: payment-service
