---
# MySQL PersistentVolumeClaim
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: mysql-pvc
  namespace: default
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 10Gi
---
# MySQL Deployment
apiVersion: apps/v1
kind: Deployment
metadata:
  name: mysql
  namespace: default
  labels:
    app: mysql
spec:
  replicas: 1
  selector:
    matchLabels:
      app: mysql
  template:
    metadata:
      labels:
        app: mysql
    spec:
      containers:
        - name: mysql
          image: mysql:8.0
          ports:
            - containerPort: 3306
          env:
            - name: MYSQL_ROOT_PASSWORD
              value: "mysecretpassword"
            - name: MYSQL_DATABASE
              value: "bookstack"
            - name: MYSQL_USER
              value: "bookstack"
            - name: MYSQL_PASSWORD
              value: "bookstack_pass"
          volumeMounts:
            - name: mysql-data
              mountPath: /var/lib/mysql
          resources:
            requests:
              memory: "512Mi"
              cpu: "500m"
            limits:
              memory: "1024Mi"
              cpu: "1000m"
      volumes:
        - name: mysql-data
          persistentVolumeClaim:
            claimName: mysql-pvc
---
# MySQL Service (Updated with regular ClusterIP)
apiVersion: v1
kind: Service
metadata:
  name: mysql
  namespace: default
  labels:
    app: mysql
spec:
  ports:
    - port: 3306
      targetPort: 3306
  selector:
    app: mysql
  clusterIP: **********
  type: ClusterIP
---
# Bookstack Deployment
apiVersion: apps/v1
kind: Deployment
metadata:
  name: bookstack
  namespace: default
  labels:
    app: bookstack
spec:
  replicas: 1
  selector:
    matchLabels:
      app: bookstack
  template:
    metadata:
      labels:
        app: bookstack
    spec:
      containers:
        - name: bookstack
          image: lscr.io/linuxserver/bookstack:latest
          ports:
            - containerPort: 80
          env:
            - name: PUID
              value: "1000"
            - name: PGID
              value: "1000"
            - name: TZ
              value: "Etc/UTC"
            - name: APP_URL
              value: "https://docs.ariadirectcorp.com"
            - name: APP_KEY
              value: "base64:5jubOA2Mr0yvXLkWmX4uLQh+yCojHo/0VgCaKXjtnIY="
            - name: DB_HOST
              value: "**********"
            - name: DB_PORT
              value: "3306"
            - name: DB_USERNAME
              value: "bookstack"
            - name: DB_PASSWORD
              value: "bookstack_pass"
            - name: DB_DATABASE
              value: "bookstack"
          resources:
            requests:
              memory: "512Mi"
              cpu: "500m"
            limits:
              memory: "1024Mi"
              cpu: "1000m"
---
apiVersion: v1
kind: Service
metadata:
  name: bookstack
  namespace: default
spec:
  selector:
    app: bookstack
  ports:
    - protocol: TCP
      port: 6875
      targetPort: 80
  type: ClusterIP
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: bookstack-ingress
  namespace: default
  annotations:
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
spec:
  rules:
    - host: docs.ariadirectcorp.com
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: bookstack
                port:
                  number: 6875