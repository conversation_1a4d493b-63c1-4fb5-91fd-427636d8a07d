apiVersion: apps/v1
kind: Deployment
metadata:
  name: notification-user-service
  labels:
    app: notification-user-service
    env: config-map
spec:
  replicas: 1
  selector:
    matchLabels:
      app: notification-user-service
  template:
    metadata:
      labels:
        app: notification-user-service
    spec:
      imagePullSecrets:
        - name: regcred
      containers:
        - name: notification-user-service
          image: 853431205376.dkr.ecr.us-west-2.amazonaws.com/notification-user-service:a4ce15fd5
          command: ["./notification-user"]
          args:
            [
              "n",
              "--ad_sqs",
              "ad_sqs",
              "--ad_aws",
              "ad_aws",
              "--ad_email",
              "ad_email",
              "--ad_website",
              "ad_website",
              "--ad_api_token",
              "ad_api_token",
              "--localize-file",
              "conf/localize.yaml",
            ]
          imagePullPolicy: IfNotPresent
          envFrom:
            - configMapRef:
                name: app-environment
