apiVersion: apps/v1
kind: Deployment
metadata:
  name: browser-service
  labels:
    app: browser-service
    env: config-map
spec:
  replicas: 0
  selector:
    matchLabels:
      app: browser-service
  template:
    metadata:
      labels:
        app: browser-service
    spec:
      imagePullSecrets:
        - name: awsecr-cred
      containers:
        - name: browser-service
          image: 853431205376.dkr.ecr.us-west-2.amazonaws.com/browser-service:a2dbf2536
          # command: ["node"]
          # args: ["app.js"]
          imagePullPolicy: IfNotPresent
          envFrom:
            - configMapRef:
                name: app-environment
