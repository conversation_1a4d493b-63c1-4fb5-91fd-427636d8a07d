apiVersion: apps/v1
kind: Deployment
metadata:
  name: packer-online-service
  labels:
    app: packer-online-service
    env: config-map
spec:
  replicas: 0
  selector:
    matchLabels:
      app: packer-online-service
  template:
    metadata:
      labels:
        app: packer-online-service
    spec:
      imagePullSecrets:
        - name: regcred
      containers:
        - name: packer-online-service
          image: 853431205376.dkr.ecr.us-west-2.amazonaws.com/packer-online-service:0c1c2ecc5Z240719T132944
          command: ["npm"]
          args: ["run", "start"]
          imagePullPolicy: IfNotPresent
          envFrom:
            - configMapRef:
                name: app-environment
