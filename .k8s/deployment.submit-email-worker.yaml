apiVersion: apps/v1
kind: Deployment
metadata:
  name: submit-email-worker-service
  labels:
    app: submit-email-worker-service
    env: config-map
spec:
  replicas: 1
  selector:
    matchLabels:
      app: submit-email-worker-service
  template:
    metadata:
      labels:
        app: submit-email-worker-service
    spec:
      imagePullSecrets:
        - name: regcred
      containers:
        - name: submit-email-worker-service
          image: 853431205376.dkr.ecr.us-west-2.amazonaws.com/submit-email-worker-service:f4693e7bc
          command: ["./submit-email-worker"]
          args:
            [
              "s",
              "--ad_sqs",
              "ad_sqs",
              "--ad_aws",
              "ad_aws",
              "--ad_s3",
              "ad_s3",
              "--ad_submit_services",
              "ad_submit_services",
              "--localize-file",
              "conf/localize.yaml",
              "--ad_db",
              "ad_db",
              "--ad_ses",
              "ad_ses",
              "--ad_email",
              "ad_email",
              "--ad_website",
              "ad_website",
              "--ad_api_token",
              "ad_api_token",
            ]
          imagePullPolicy: IfNotPresent
          envFrom:
            - configMapRef:
                name: app-environment
