apiVersion: v1
kind: Service # Create service
metadata:
  name: postgres-node-port # Sets the service name
  labels:
    app: postgres # Defines app to create service for
spec:
  type: NodePort # Sets the service type
  ports:
    - port: 5432 # Sets the port to run the postgres application
      nodePort: 30000
  selector:
    app: postgres
# ---
# apiVersion: v1
# kind: Service
# metadata:
#   name: postgres-load-balancer
#   labels:
#     app: postgres
# spec:
#   type: LoadBalancer
#   clusterIP: **********
#   ports:
#     - port: 5432
#       protocol: TCP
#       targetPort: 5432
#   selector:
#     app: postgres
---
apiVersion: v1
kind: Service # Create service
metadata:
  name: postgres # Sets the service name
  labels:
    app: postgres # Defines app to create service for
spec:
  type: ClusterIP # Sets the service type
  ports:
    - port: 5432 # Sets the port to run the postgres application
      targetPort: 5432
  clusterIP: **********
  selector:
    app: postgres
