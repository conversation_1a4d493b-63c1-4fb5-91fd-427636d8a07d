apiVersion: apps/v1
kind: Deployment # Create a deployment
metadata:
  name: meili-search-service # Set the name of the deployment
spec:
  replicas: 1 # Set 3 deployment replicas
  selector:
    matchLabels:
      app: meili-search-service
  template:
    metadata:
      labels:
        app: meili-search-service
    spec:
      containers:
        - name: meili-search
          image: getmeili/meilisearch:v0.30
          imagePullPolicy: "IfNotPresent"
          ports:
            - containerPort: 7700 # Exposing the container port 5432 for meili-searchQL client connections.
          volumeMounts:
            - mountPath: /meili_data/data.ms
              name: meili-search-data
      volumes:
        - name: meili-search-data
          persistentVolumeClaim:
            claimName: meili-search-volume-claim
---
apiVersion: v1
kind: PersistentVolume # Create PV
metadata:
  name: meili-search-volume # Sets PV name
  labels:
    type: local # Sets PV's type
    app: meili-search-service
spec:
  storageClassName: manual
  capacity:
    storage: 1Gi # Sets PV's size
  accessModes:
    - ReadWriteMany
  hostPath:
    path: "data.ms" # Sets PV's host path
---
apiVersion: v1
kind: PersistentVolumeClaim # Create PVC
metadata:
  name: meili-search-volume-claim # Sets PVC's name
  labels:
    app: meili-search-service # Defines app to create PVC for
spec:
  storageClassName: manual
  accessModes:
    - ReadWriteMany
  resources:
    requests:
      storage: 1Gi # Sets PVC's size
---
apiVersion: v1
kind: Service
metadata:
  name: meili-search-service
  labels:
    app: meili-search-service
spec:
  type: ClusterIP
  ports:
    - port: 7700
      targetPort: 7700
      protocol: TCP
  clusterIP: **********
  selector:
    app: meili-search-service
