--- # ---
# apiVersion: v1
# kind: Service # Create service
# metadata:
#   name: user-management-service-node-port # Sets the service name
#   labels:
#     app: user-management-service # Defines app to create service for
# spec:
#   type: NodePort # Sets the service type
#   ports:
#     - port: 3000
#       targetPort: 3000
#       protocol: TCP
#       nodePort: 30002
#   selector:
#     app: user-management-service
apiVersion: v1
kind: Service
metadata:
  name: user-management-load-balancer
  labels:
    app: user-management-service
spec:
  type: LoadBalancer
  clusterIP: **********
  ports:
    - port: 80
      protocol: TCP
      targetPort: 3000
  selector:
    app: user-management-service
