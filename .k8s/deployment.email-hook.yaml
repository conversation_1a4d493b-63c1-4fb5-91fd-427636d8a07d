apiVersion: apps/v1
kind: Deployment
metadata:
  name: email-hook-service
  labels:
    app: email-hook-service
    env: config-map
spec:
  replicas: 1
  selector:
    matchLabels:
      app: email-hook-service
  template:
    metadata:
      labels:
        app: email-hook-service
    spec:
      imagePullSecrets:
        - name: regcred
      containers:
        - name: email-hook-service
          image: 853431205376.dkr.ecr.us-west-2.amazonaws.com/email-hook-service:ac9984fdbZ250519T115618
          command: ["./email-hook"]
          args: ["svc"]
          imagePullPolicy: IfNotPresent
          envFrom:
            - configMapRef:
                name: app-environment

