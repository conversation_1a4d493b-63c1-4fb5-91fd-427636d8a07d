apiVersion: apps/v1
kind: Deployment
metadata:
  name: carrier-service
  labels:
    app: carrier-service
    env: config-map
spec:
  replicas: 1
  selector:
    matchLabels:
      app: carrier-service
  template:
    metadata:
      labels:
        app: carrier-service
    spec:
      imagePullSecrets:
        - name: regcred
      containers:
        - name: carrier-service
          image: 853431205376.dkr.ecr.us-west-2.amazonaws.com/carrier-service:0c9905142
          command: ["./carrier"]
          args:
            [
              "s",
              "--ad_sqs",
              "ad_sqs",
              "--ad_aws",
              "ad_aws",
              "--ad_s3",
              "ad_s3",
              "--ad_fedex",
              "ad_fedex",
              "--ad_api_token",
              "ad_api_token",
            ]
          imagePullPolicy: IfNotPresent
          envFrom:
            - configMapRef:
                name: app-environment
