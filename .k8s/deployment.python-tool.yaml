apiVersion: apps/v1
kind: Deployment
metadata:
  name: python-tool-service
  labels:
    app: python-tool-service
    env: config-map
spec:
  replicas: 1
  selector:
    matchLabels:
      app: python-tool-service
  template:
    metadata:
      labels:
        app: python-tool-service
    spec:
      imagePullSecrets:
        - name: regcred
      containers:
        - name: python-tool-service
          image: 853431205376.dkr.ecr.us-west-2.amazonaws.com/python-tool-service:70f79d458Z240724T133020
          command: ["./command_script"]
          args: ["svc"]
          imagePullPolicy: IfNotPresent
          ports:
            - containerPort: 3000
          envFrom:
            - configMapRef:
                name: app-environment
---
apiVersion: v1
kind: Service
metadata:
  name: python-tool-service
  labels:
    app: python-tool-service
spec:
  type: ClusterIP
  ports:
    - port: 3000
      targetPort: 3000
      protocol: TCP
  selector:
    app: python-tool-service
