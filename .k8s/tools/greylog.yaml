# mongodb-deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: mongodb
  labels:
    app: mongodb
spec:
  selector:
    matchLabels:
      app: mongodb
  template:
    metadata:
      labels:
        app: mongodb
    spec:
      containers:
      - name: mongodb
        image: mongo:3
        ports:
        - containerPort: 27017
---
# mongodb-service.yaml
apiVersion: v1
kind: Service
metadata:
  name: mongodb
spec:
  selector:
    app: mongodb
  ports:
  - port: 27017
    targetPort: 27017
---
# elasticsearch-deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: elasticsearch
  labels:
    app: elasticsearch
spec:
  selector:
    matchLabels:
      app: elasticsearch
  template:
    metadata:
      labels:
        app: elasticsearch
    spec:
      containers:
      - name: elasticsearch
        image: docker.elastic.co/elasticsearch/elasticsearch-oss:6.8.5
        env:
        - name: http.host
          value: "0.0.0.0"
        - name: transport.host
          value: "localhost"
        - name: network.host
          value: "0.0.0.0"
        - name: "ES_JAVA_OPTS"
          value: "-Xms512m -Xmx512m"
        resources:
          limits:
            memory: 1Gi
        ports:
        - containerPort: 9200
        - containerPort: 9300
---
# elasticsearch-service.yaml
apiVersion: v1
kind: Service
metadata:
  name: elasticsearch
spec:
  selector:
    app: elasticsearch
  ports:
  - name: rest
    port: 9200
    targetPort: 9200
  - name: inter-node
    port: 9300
    targetPort: 9300
---
# graylog-deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: graylog
  labels:
    app: graylog
spec:
  selector:
    matchLabels:
      app: graylog
  template:
    metadata:
      labels:
        app: graylog
    spec:
      containers:
      - name: graylog
        image: graylog/graylog:3.2
        env:
        - name: GRAYLOG_PASSWORD_SECRET
          value: "somepasswordpepper"
        - name: GRAYLOG_ROOT_PASSWORD_SHA2
          value: "8c6976e5b5410415bde908bd4dee15dfb167a9c873fc4bb8a81f6f2ab448a918"
        - name: GRAYLOG_HTTP_EXTERNAL_URI
          value: "https://log.ariadirectcorp.com/"
        - name: GRAYLOG_MONGODB_URI
          value: "mongodb://mongodb:27017/graylog"
        - name: GRAYLOG_ELASTICSEARCH_HOSTS
          value: "http://elasticsearch:9200"
        # Gmail SMTP Configuration
        - name: GRAYLOG_TRANSPORT_EMAIL_ENABLED
          value: "true"
        - name: GRAYLOG_TRANSPORT_EMAIL_HOSTNAME
          value: "smtp.gmail.com"
        - name: GRAYLOG_TRANSPORT_EMAIL_PORT
          value: "587"
        - name: GRAYLOG_TRANSPORT_EMAIL_USE_AUTH
          value: "true"
        - name: GRAYLOG_TRANSPORT_EMAIL_USE_TLS
          value: "true"
        - name: GRAYLOG_TRANSPORT_EMAIL_USE_SSL
          value: "false"
        - name: GRAYLOG_TRANSPORT_EMAIL_AUTH_USERNAME
          value: "<EMAIL>"
        - name: GRAYLOG_TRANSPORT_EMAIL_AUTH_PASSWORD
          value: "mnjwkhadbzynwzmq" 
        - name: GRAYLOG_TRANSPORT_EMAIL_FROM_EMAIL
          value: "<EMAIL>" 
        - name: GRAYLOG_TRANSPORT_EMAIL_SUBJECT_PREFIX
          value: "[Graylog]"
        ports:
        - containerPort: 9000
        - containerPort: 1514
        - containerPort: 12201
---
# graylog-service.yaml
apiVersion: v1
kind: Service
metadata:
  name: graylog
spec:
  type: NodePort
  selector:
    app: graylog
  ports:
  - name: web
    port: 9000
    targetPort: 9000
    nodePort: 30900
  - name: syslog-tcp
    port: 1514
    targetPort: 1514
    nodePort: 31514
  - name: gelf-tcp
    port: 12201
    targetPort: 12201
    nodePort: 32201
  - name: syslog-udp
    port: 1514
    targetPort: 1514
    protocol: UDP
    nodePort: 31515
  - name: gelf-udp
    port: 12201
    targetPort: 12201
    protocol: UDP
    nodePort: 32202