apiVersion: apps/v1
kind: Deployment
metadata:
  name: uploader-service
  labels:
    app: uploader-service
    env: config-map
spec:
  replicas: 1
  selector:
    matchLabels:
      app: uploader-service
  template:
    metadata:
      labels:
        app: uploader-service
    spec:
      imagePullSecrets:
        - name: regcred
      containers:
        - name: uploader-service
          image: 853431205376.dkr.ecr.us-west-2.amazonaws.com/uploader-service:latest
          command: ["./app"]
          args: []
          imagePullPolicy: Always
          ports:
            - containerPort: 3000
          envFrom:
            - configMapRef:
                name: app-environment
---
apiVersion: v1
kind: Service
metadata:
  name: uploader-service
  labels:
    app: uploader-service
spec:
  type: ClusterIP
  ports:
    - port: 3000
      targetPort: 3000
      protocol: TCP
  selector:
    app: uploader-service
