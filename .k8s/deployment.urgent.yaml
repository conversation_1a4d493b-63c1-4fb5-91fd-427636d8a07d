apiVersion: apps/v1
kind: Deployment
metadata:
  name: urgent-service
  labels:
    app: urgent-service
    env: config-map
spec:
  replicas: 1
  selector:
    matchLabels:
      app: urgent-service
  template:
    metadata:
      labels:
        app: urgent-service
    spec:
      imagePullSecrets:
        - name: regcred
      containers:
        - name: urgent-service
          image: 853431205376.dkr.ecr.us-west-2.amazonaws.com/urgent-service:latest
          command: ["serve","-d","build"]
          args: []
          imagePullPolicy: Always
          ports:
            - containerPort: 3000
          envFrom:
            - configMapRef:
                name: app-environment
---
apiVersion: v1
kind: Service
metadata:
  name: urgent-service
  labels:
    app: urgent-service
spec:
  type: ClusterIP
  ports:
    - port: 3000
      targetPort: 3000
      protocol: TCP
  selector:
    app: urgent-service
