apiVersion: apps/v1
kind: Deployment
metadata:
  name: mrz-parser-service
  labels:
    app: mrz-parser-service
    env: config-map
spec:
  replicas: 1
  selector:
    matchLabels:
      app: mrz-parser-service
  template:
    metadata:
      labels:
        app: mrz-parser-service
    spec:
      imagePullSecrets:
        - name: regcred
      containers:
        - name: mrz-parser-service
          image: 853431205376.dkr.ecr.us-west-2.amazonaws.com/mrz-parser-service:7e8bb7562Z250517T105747
          command: ["./mrz_parser"]
          args: ["--db-config", "ad_db", "--ad_endpoint", "ad_endpoint"]
          imagePullPolicy: IfNotPresent
          ports:
            - containerPort: 3000
          envFrom:
            - configMapRef:
                name: app-environment
---
apiVersion: v1
kind: Service
metadata:
  name: mrz-parser-service
  labels:
    app: mrz-parser-service
spec:
  type: ClusterIP
  ports:
    - port: 3000
      targetPort: 3000
      protocol: TCP
  selector:
    app: mrz-parser-service
