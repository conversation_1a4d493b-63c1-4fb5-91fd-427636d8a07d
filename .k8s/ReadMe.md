minikube start
minikube addons enable ingress

# aws ecr get-login-password --region us-west-2 => Can be use in local

# docker login -u AWS ************.dkr.ecr.us-west-2.amazonaws.com

kubectl create secret docker-registry regcred \
--docker-server=${AWS_ACCOUNT}.dkr.ecr.${AWS_REGION}.amazonaws.com \
--docker-username=A<PERSON> \
--docker-password=$(aws ecr get-login-password) \
--namespace=health-check

kubectl create secret docker-registry regcred \
--docker-server=************.dkr.ecr.us-west-2.amazonaws.com \
--docker-username=A<PERSON> \
--docker-password=$(aws ecr get-login-password) 
