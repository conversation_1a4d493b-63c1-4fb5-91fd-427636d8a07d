apiVersion: apps/v1
kind: Deployment
metadata:
  name: passport-photo-service
  labels:
    app: passport-photo-service
    env: config-map
spec:
  replicas: 1
  selector:
    matchLabels:
      app: passport-photo-service
  template:
    metadata:
      labels:
        app: passport-photo-service
    spec:
      imagePullSecrets:
        - name: regcred
      containers:
        - name: passport-photo-service
          image: 853431205376.dkr.ecr.us-west-2.amazonaws.com/passport-photo-service:7e8bb7562Z250517T105935
          command: ["npm"]
          args: ["run", "start"]
          imagePullPolicy: IfNotPresent
          ports:
            - containerPort: 3000
          envFrom:
            - configMapRef:
                name: app-environment
---
apiVersion: v1
kind: Service
metadata:
  name: passport-photo-service
  labels:
    app: passport-photo-service
spec:
  type: ClusterIP
  ports:
    - port: 3000
      targetPort: 3000
      protocol: TCP
  selector:
    app: passport-photo-service
