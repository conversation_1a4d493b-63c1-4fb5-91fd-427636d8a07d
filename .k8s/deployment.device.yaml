apiVersion: apps/v1
kind: Deployment
metadata:
  name: device-service
  labels:
    app: device-service
    env: config-map
spec:
  replicas: 1
  selector:
    matchLabels:
      app: device-service
  template:
    metadata:
      labels:
        app: device-service
    spec:
      imagePullSecrets:
        - name: regcred
      containers:
        - name: device-service
          image: ************.dkr.ecr.us-west-2.amazonaws.com/devices-service:31219d883
          command: ["./devices"]
          args:
            [
              "svc",
              "--ad_firebase",
              "ad_firebase",
              "--db-config",
              "ad_db",
              "--ad_secrets",
              "ad_secrets",
              "--account-service-config",
              "ad_user_account_service",
              "--ad_api_token",
              "ad_api_token",
            ]
          imagePullPolicy: IfNotPresent
          ports:
            - containerPort: 3000
          envFrom:
            - configMapRef:
                name: app-environment
---
apiVersion: v1
kind: Service
metadata:
  name: device-service
  labels:
    app: device-service
spec:
  type: ClusterIP
  ports:
    - port: 3000
      targetPort: 3000
      protocol: TCP
  selector:
    app: device-service
