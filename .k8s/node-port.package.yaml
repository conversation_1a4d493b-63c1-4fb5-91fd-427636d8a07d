apiVersion: v1
kind: Service # Create service
metadata:
  name: package-service-node-port # Sets the service name
  labels:
    app: package-service # Defines app to create service for
spec:
  type: NodePort # Sets the service type
  ports:
    - port: 3000
      targetPort: 3000
      protocol: TCP
      nodePort: 30001
  selector:
    app: package-service
---
apiVersion: v1
kind: Service # Create service
metadata:
  name: meili-search-service-node-port # Sets the service name
  labels:
    app: meili-search-service # Defines app to create service for
spec:
  type: NodePort # Sets the service type
  ports:
    - port: 7700
      targetPort: 7700
      protocol: TCP
      nodePort: 30003
  selector:
    app: meili-search-service
