apiVersion: apps/v1
kind: Deployment
metadata:
  name: version-service
  labels:
    app: version-service
    env: config-map
spec:
  replicas: 1
  selector:
    matchLabels:
      app: version-service
  template:
    metadata:
      labels:
        app: version-service
    spec:
      imagePullSecrets:
        - name: regcred
      containers:
        - name: version-service
          image: 853431205376.dkr.ecr.us-west-2.amazonaws.com/version-service:8dc121c17
          command: ["./app_version"]
          args:
            [
              "svc",
              "--version-file-bucket",
              "ad-app-version",
              "--version-file-key",
              "app-versions/app-versions.json",
              "--s3-region",
              "us-west-2",
            ]
          imagePullPolicy: IfNotPresent
          ports:
            - containerPort: 3000
          envFrom:
            - configMapRef:
                name: app-environment
---
apiVersion: v1
kind: Service
metadata:
  name: version-service
  labels:
    app: version-service
spec:
  type: ClusterIP
  ports:
    - port: 3000
      targetPort: 3000
      protocol: TCP
  selector:
    app: version-service
