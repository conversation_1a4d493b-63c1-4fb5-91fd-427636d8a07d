apiVersion: apps/v1
kind: Deployment
metadata:
  name: helper-service
  labels:
    app: helper-service
    env: config-map
spec:
  replicas: 1
  selector:
    matchLabels:
      app: helper-service
  template:
    metadata:
      labels:
        app: helper-service
    spec:
      imagePullSecrets:
        - name: regcred
      containers:
        - name: helper-service
          image: 853431205376.dkr.ecr.us-west-2.amazonaws.com/helper-service:56a837948Z250528T165649
          command: ["npm"]
          args: ["run", "start"]
          imagePullPolicy: IfNotPresent
          ports:
            - containerPort: 3000
          envFrom:
            - configMapRef:
                name: app-environment
---
apiVersion: v1
kind: Service
metadata:
  name: helper-service
  labels:
    app: helper-service
spec:
  type: ClusterIP
  ports:
    - port: 3000
      targetPort: 3000
      protocol: TCP
  selector:
    app: helper-service
