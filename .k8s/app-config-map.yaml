# Create ConfigMap postgres-secret for the postgres app
# Define default database name, user, and password
apiVersion: v1
kind: ConfigMap
metadata:
  name: app-environment
  labels:
    env: config-map
data:
  AWS_ACCESS_KEY_ID: ********************
  AWS_SECRET_ACCESS_KEY: nqCvZGisSd1g3u8fbYcoTESx4XOtlGnz4uBqaiL5
  DB_HOST: **********
  DB_PASSWORD: ariadirect2020
  ad_api_token: '{"token": "2hNwPul_SppFM_iUlV1Wng"}'
************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
  ad_authorize: '{"authorize_api_token": "7g5maWV3J4n4", "authorize_key": "99nRhg297B3kjVGS", "authorize_mode": "sandbox", "authorize_endpoint": "https://apitest.authorize.net/xml/v1/request.api", "authorize_form_post_url": "https://test.authorize.net/payment/payment", "authorize_webhook_url": "https://apitest.authorize.net/rest/v1", "authorize_webhook_id": "9104bfbe-0bb7-4928-9170-e5be03ec912b", "authorize_webhook_sign": "6821C129F820F1822E46E9AB544E2211CBC24D03810D337D2DD344DDA87ACE0A0F5481AC305DA715345F0AC7FCFBF5A67442F9E7CA0ADF125871534B17125CAA", "authorize_webhook_callback": "https://api.ariadirectcorp.com/v1/payment/payments/callback/AUTHORIZENET", "authorize_redirect_url": "https://dev.ariadirect.com/dashboard/payment/payment-result/authorizenet", "authorize_redirect_cancel_url": "https://dev.ariadirect.com/dashboard/my-cart?tab=my_cart"}'
  ad_aws: '{"region": "ap-northeast-1", "account": "************", "ecr": "************.dkr.ecr.us-west-2.amazonaws.com"}'
  ad_db: '{"username": "aria", "password": "ariadirect2020", "port": 5432, "dbname": "ariadirect_stag", "read_host": "**********", "write_host": "**********"}'
  ad_meilisearch: '{"host": "http://**********:7700"}'
  ad_document_template: '{"emails": "email_template.sql"}'
  ad_email: '{"info": "*******", "finance": "*******", "visas": "*******", "services": "*******", "support": "*******", "admin": "*******", "supervisor": "*******", "env": "staging"}'
  ad_endpoint: '{"api_base_url": "https://api.ariadirectcorp.com", "web_base_url": "https://dev.ariadirect.com"}'
  ad_env: "stag"
  ad_es: '{"region": "us-west-2", "endpoint": "vpc-ad-elasticsearch-produs-6ajlxbv4ed4e7sjp5t5gvurmfq.us-west-2.es.amazonaws.com", "port": 443}'
  ad_fedex: '{"env": "sandbox", "key": "9BDH9o9APvHngAHj", "password": "yl15UagMMKdNIwf9Yr4W8ke9x", "account": "*********", "meter": "*********", "url": "https://wsbeta.fedex.com:443/web-services"}'
***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
  ad_onepay: '{"onepay_merchant_id": "TESTONEPAY", "onepay_merchant_order": "JSECURETEST01", "onepay_user": "op01", "onepay_password": "op123456", "onepay_api_key": "6BEB2546", "onepay_api_secret": "6D0870CDE5F24F34F3915FB0045120DB", "onepay_endpoint": "https://mtf.onepay.vn/paygate/vpcpay.op", "onepay_callback": "https://dev.ariadirect.com/dashboard/payment/payment-result/onepay", "onepay_query_dc": "https://mtf.onepay.vn/msp/api/v1/vpc/invoices/queries", "vpbank_exchange_rate": "https://portal.vietcombank.com.vn/Usercontrols/TVPortal.TyGia/pXML.aspx", "onepay_enabled": true}'
  ad_package_service: '{"host_name": "api.ariadirectcorp.com/v1/pkg"}'
  ad_packer_service: '{"search_url": "https://api.ariadirectcorp.com/v1/search-svc", "host_name": "api.ariadirectcorp.com/v1/packer"}'
  ad_passport_photo_service: '{"background_token": "eab4a8e27c8b4b4e95e71f85e50e1548"}'
  ad_payment_service: '{"host_name": "api.ariadirectcorp.com/v1/payment"}'
  ad_paypal: '{"paypal_api_key": "AfJkegfbJz1eCUcnn5Uey-ne4ODU0Zd2yc9IS2XIqdp47kkz6OoWpqST11zwDXs0VAqDRaemLp8Lez8M", "paypal_api_secret": "EKRGW_jR2t03TIN_UAH3p47zlevcAByzILXL0Z6IrO352DUP9B7m7ZSNXFAljz7ZGbs9__cogLV6MrhY", "paypal_endpoint": "https://api.sandbox.paypal.com", "paypal_return": "https://dev.ariadirect.com/dashboard/payment/payment-result/paypal", "paypal_cancel": "https://dev.ariadirect.com/dashboard/my-cart?tab=my_cart", "paypal_callback": "https://api.ariadirectcorp.com/v1/payment/payments/callback/paypal", "paypal_webhook_id": "9CN10343J3906151E"}'
  ad_s3: '{"ariadirect_prod_passport_images": "ad-prodjp-passport-images-ap-northeast-1", "ariadirect_prod_applications": "ad-prodjp-applications-ap-northeast-1", "ariadirect_prod_notification-events": "ad-prodjp-notification-events-ap-northeast-1", "ariadirect_prod_atlas_cache_s3_bucket": "ad-produs-atlas-cache-us-west-2", "ariadirect_prod_document_templates": "ad-prodjp-document-template-ap-northeast-1", "ariadirect_prod_ai_service": "ad-prodjp-ai-service-ap-northeast-1", "ariadirect_prod_corporation_invoices": "ad-prodjp-corporation-invoices-ap-northeast-1", "ariadirect_prod_corporation_logo": "ad-prodjp-corporation-logo-ap-northeast-1", "ariadirect_prod_traveler_profile": "ad-prodjp-traveler-profile-ap-northeast-1","ariadirect_prod_album":"ad-prodjp-album-ap-northeast-1"}'
  ad_secrets: '{"jwt": "XAivjIeK1ZV7lUscDHU4"}'
  ad_ses: '{"region": "us-west-2"}'
  ad_sqs: '{"url_prefix": "https://sqs.ap-northeast-1.amazonaws.com/************", "dead_letter_suffix": "-dead-letter", "atlas_sqs_name": "ad-prodjp-atlas", "notification_sqs_name": "ad-prodjp-notification", "notification_user_sqs_name": "ad-prodjp-notification-user", "packer_offline_sqs_name": "ad-prodjp-packer-offline", "packer_online_sqs_name": "ad-prodjp-packer-online", "packer_online_captcha_sqs_name": "ad-prodjp-packer-online-captcha", "shipment_sqs_name": "ad-prodjp-shipment", "submit_sqs_name": "ad-prodjp-submit", "website_sqs_name": "ad-prodjp-website"}'
  ad_stripe: '{"secret": "sk_test_51J3LVlAuVesI6jXd2dwjqT7EhB8Jmfq5IckbC6Z4J0e0fPO08m0rknOyucZ2xEq2EQBCyMloVfPiWRYtX0syVDNb00QkuiODYE"}'
  ad_submit_services: '{"env": "staging"}'
  ad_user_account_service: '{"host_name": "http://**********/v1/ums", "url": "https://dev.ariadirect.com/", "googlecid": "************-pu4vskq4g75k53d3gsbq4p938mjo9snm.apps.googleusercontent.com", "googlecsecret": "H_S6N2oO77GxUNv_fVqsciB_", "second_clientid": "************-20428rrf358a45s8s492gkn056ulodu4.apps.googleusercontent.com", "ios_clientid": "************-det0e0p3vs9mgg3bm5g6teshnkmpiaus.apps.googleusercontent.com", "linkedin_request_uri": "https://dev.ariadirect.com/oauth/linkedin", "linkedin_client_id": "865opblbnr8k0j", "linkedin_client_secret": "XuJMiH5jIEo3p7k7", "okta_cid": "0oa18doua2DbRATAF5d7", "okta_csecret": "rFxohP5Eclgzd3rlV7n1wjWE5bGXERCAuMUo96U4", "okta_app_url": "https://dev-********.okta.com/oauth2/default"}'
  ad_walgreen: '{"service_url": "https://services-qa.walgreens.com", "api_key": "d12ddc87a36f1cfb422dccb4ff0a7184", "print_photo_product_id": "8770001", "email": "*******", "first_name": "Cindy", "last_name": "Ngo", "phone": "**********"}'
  ad_watchdog_service: '{"package_check_frequency": "0 * * * *", "package_action_frequency": "0 * * * *", "package_complete_days": 30, "package_incomplete_days": 20, "package_process_days": 10, "application_check_frequency": "0 0 * * *", "application_action_frequency": "0 0 * * *", "application_review_hours": 1, "slack_api_key": "********************************************************", "slack_channel_id": "CV7BCN5A7", "remind_passport_url": "https://ariadirect.page.link/passport-service-staging", "remind_visa_url": "https://ariadirect.page.link/visa-service-staging"}'
  ad_website: '{"host_name": "https://dev.ariadirect.com"}'
  ad_wechatpay: '{"payment_url_wechatpay": "https://api.ariadirectcorp.com/v1/payment/payments/callback/wechatpay", "wechat_pay_api_key": "tE7Wog7sPofaqehObradUwRabOb2inac", "wechat_pay_account_id": "**********", "wechat_pay_app_id": "wx80a3055e2a3b98d2"}'
  ad_zellepay: '{"zellepay_contact_email": "*******", "zellepay_contact_phone": "+***********", "bank_1_name": "Ng\u00e2n h\u00e0ng th\u01b0\u01a1ng m\u1ea1i c\u1ed5 ph\u1ea7n \u00c1 Ch\u00e2u (ACB)", "bank_1_owner": "C\u00f4ng ty TNHH AriaDirect", "bank_1_number": "*********"}'
