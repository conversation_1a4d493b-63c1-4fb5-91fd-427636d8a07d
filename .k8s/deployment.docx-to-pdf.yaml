apiVersion: apps/v1
kind: Deployment
metadata:
  name: docx-to-pdf-service
  labels:
    app: docx-to-pdf-service
    env: config-map
spec:
  replicas: 0
  selector:
    matchLabels:
      app: docx-to-pdf-service
  template:
    metadata:
      labels:
        app: docx-to-pdf-service
    spec:
      imagePullSecrets:
        - name: regcred
      containers:
        - name: docx-to-pdf-service
          image: 853431205376.dkr.ecr.us-west-2.amazonaws.com/docx-to-pdf-service:latest
          command: ["python"]
          args: ["routes.py"]
          imagePullPolicy: IfNotPresent
          ports:
            - containerPort: 5000
          envFrom:
            - configMapRef:
                name: app-environment
---
apiVersion: v1
kind: Service
metadata:
  name: docx-to-pdf-service
  labels:
    app: docx-to-pdf-service
spec:
  type: ClusterIP
  ports:
    - port: 3000
      targetPort: 5000
      protocol: TCP
  selector:
    app: docx-to-pdf-service
