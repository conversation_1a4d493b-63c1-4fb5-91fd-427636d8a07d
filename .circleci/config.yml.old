---
version: 2.1

jobs:
  setup_and_deploy:
    machine:
      image: ubuntu-2204:2022.04.2
    steps:
      - checkout
      - run:
          name: Python Version
          command: python3 --version
      - run: pip3 install ansible boto3
      - run:
          name: Ansible Version
          command: ansible --version
      - run:
          name: Configure AWS Access Key ID
          command: |
            aws configure set aws_access_key_id $AWS_ACCESS_KEY_ID
      - run:
          name: Configure AWS Secret Access Key
          command: |
            aws configure set aws_secret_access_key $AWS_SECRET_ACCESS_KEY

      - run:
          name: Configure AWS default region
          command: |
            aws configure set region us-east-2

      - when:
          condition:
            equal: [dev, << pipeline.git.branch >>]
          steps:
            - run:
                name: Deploy service staging
                no_output_timeout: 20m
                command: |
                  make deploy-jp
      - when:
          condition:
            equal: [master, << pipeline.git.branch >>]
          steps:
            - run:
                name: Deploy service production
                no_output_timeout: 20m
                command: |
                  make deploy-us

workflows:
  version: 2
  build_and_deploy:
    jobs:
      - setup_and_deploy
