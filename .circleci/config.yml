---
version: 2.1

jobs:
  setup_and_deploy:
    machine:
      image: ubuntu-2204:2024.05.1
    steps:
      # - checkout
      # - add_ssh_keys:
      #     fingerprints:
      #       - "2b:01:1f:8f:6f:a5:90:f8:c2:35:81:c8:22:dc:ce:66"
      - when:
          condition:
            equal: [dev, << pipeline.git.branch >>]
          steps:
            - run:
                name: Deploy service staging
                no_output_timeout: 20m
                command: |
                  # ssh root@************** "sh ~/aria/.k8s/scripts/deployment.sh";
                  ssh leo@************** "sh ~/aria/.k8s/scripts/deployment.sh";
      - when:
          condition:
            equal: [master, << pipeline.git.branch >>]
          steps:
            - run:
                name: Deploy service production
                no_output_timeout: 20m
                command: |
                  make deploy-us

workflows:
  version: 2
  build_and_deploy:
    jobs:
      - setup_and_deploy
