const axios = require('axios');
const fs = require('fs');
const { APPOINTMENT_STATE_LOCATIONS } = require('./mapping');
const US_CITIES = require('../../shared/us_cities.json')

const appointment_states = (req, res) => {
    res.json({
        sucess: true,
        type: 'select',
        data: Object.keys(APPOINTMENT_STATE_LOCATIONS).map(l => ({
            text: US_CITIES.find(v => v.state_code === l).state_name,
            value: l,
        })),
    })
}


const appointment_locations = (req, res) => {
    const { appointment_desired_appointment_info_state } = req.query;
    res.json({
        success: true,
        type: 'select',
        data: APPOINTMENT_STATE_LOCATIONS[appointment_desired_appointment_info_state || 'AZ'].map(v => ({
            text: v,
            value: v,
        })),
    })
}

module.exports = {
    appointment_states,
    appointment_locations,
}