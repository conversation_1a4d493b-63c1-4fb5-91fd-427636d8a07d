const fs = require('fs');
const axios = require('axios');
const uuid = require('uuid');
const moment = require('moment');
const { connect } = require('../../shared/browser/index.js');
const { PuppeteerScreenRecorder } = require('puppeteer-screen-recorder');
const PhoneNumber = require('libphonenumber-js');
const { exec } = require('child_process');
const util = require('util');
const BTAPI = require('../../shared/bt_api');
const { s3_upload_buff, get_order_task_pod_data, sleep, get_country_iso2, get_country_name_v2 } = require('../../shared/helpers');
const countries = {
    "AFG": "AFGHANISTAN",
    "ALA": "ALAND ISLANDS",
    "ALB": "ALBANIA",
    "DZA": "ALGERIA",
    "AND": "ANDORRA",
    "AGO": "ANGOLA",
    "AIA": "ANGUILLA",
    "ATG": "ANTIGUA AND BARBUDA",
    "ARG": "ARGENTINA",
    "ARM": "ARMENIA",
    "ABW": "ARUBA",
    "AUS": "AUSTRALIA",
    "AUT": "AUSTRIA",
    "AZE": "AZERBAIJAN",
    "BHS": "BAHAMAS",
    "BHR": "BAHRAIN",
    "BGD": "BANGLADESH",
    "BRB": "BARBADOS",
    "BLR": "BELARUS",
    "BEL": "BELGIUM",
    "BLZ": "BELIZE",
    "BEN": "BENIN",
    "BMU": "BERMUDA",
    "BTN": "BHUTAN",
    "BOL": "BOLIVIA",
    "BES": "BONAIRE, ST EUSTATIUS, SABA",
    "BIH": "BOSNIA AND HERZEGOWINA",
    "BWA": "BOTSWANA",
    "BRA": "BRAZIL",
    "BRN": "BRUNEI DARUSSALAM",
    "BGR": "BULGARIA",
    "BFA": "BURKINA FASO",
    "MMR": "BURMA",
    "BDI": "BURUNDI",
    "KHM": "CAMBODIA",
    "CMR": "CAMEROON",
    "CPV": "CAPE VERDE",
    "CYM": "CAYMAN ISLANDS",
    "CAF": "CENTRAL AFRICAN REPUBLIC",
    "TCD": "CHAD",
    "CHL": "CHILE",
    "CHN": "CHINA",
    "COL": "COLOMBIA",
    "COG": "CONGO",
    "COK": "COOK ISLANDS",
    "CRI": "COSTA RICA",
    "CIV": "COTE D'IVOIRE",
    "HRV": "CROATIA",
    "CUB": "CUBA",
    "CUW": "CURACAO",
    "CYP": "CYPRUS",
    "CZE": "CZECH REPUBLIC",
    "COD": "DEMOCRATIC REPUBLIC OF THE CONGO",
    "DNK": "DENMARK",
    "DJI": "DJIBOUTI",
    "DMA": "DOMINICA",
    "DOM": "DOMINICAN REPUBLIC",
    "TLS": "EAST TIMOR",
    "ECU": "ECUADOR",
    "EGY": "EGYPT",
    "SLV": "EL SALVADOR",
    "GNQ": "EQUATORIAL GUINEA",
    "ERI": "ERITREA",
    "EST": "ESTONIA",
    "ETH": "ETHIOPIA",
    "FJI": "FIJI",
    "FIN": "FINLAND",
    "FRA": "FRANCE",
    "GUF": "FRENCH GUIANA",
    "PYF": "FRENCH POLYNESIA",
    "GAB": "GABON",
    "GMB": "GAMBIA",
    "GEO": "GEORGIA",
    "DDR": "GERMAN DEMOCRATIC REPUBLIC",
    "DEU": "GERMANY",
    "GHA": "GHANA",
    "GRC": "GREECE",
    "GRD": "GRENADA",
    "GTM": "GUATEMALA",
    "GGY": "GUERNSEY",
    "GIN": "GUINEA",
    "GNB": "GUINEA-BISSAU",
    "GUY": "GUYANA",
    "HTI": "HAITI",
    "HND": "HONDURAS",
    "HKG": "HONG KONG",
    "HUN": "HUNGARY",
    "ISL": "ICELAND",
    "IND": "INDIA",
    "IDN": "INDONESIA",
    "IRN": "IRAN (ISLAMIC REPUBLIC OF)",
    "IRQ": "IRAQ",
    "IRL": "IRELAND",
    "IMN": "ISLE OF MAN",
    "ISR": "ISRAEL",
    "ITA": "ITALY",
    "JAM": "JAMAICA",
    "JPN": "JAPAN",
    "JEY": "JERSEY",
    "JOR": "JORDAN",
    "KAZ": "KAZAKHSTAN",
    "KEN": "KENYA",
    "KIR": "KIRIBATI",
    "PRK": "KOREA - DEMOCRATIC PEOPLE'S REPUBLIC OF",
    "KOR": "KOREA - REPUBLIC OF",
    "XKX": "KOSOVO",
    "KWT": "KUWAIT",
    "KGZ": "KYRGYZSTAN",
    "LAO": "LAOS",
    "LVA": "LATVIA",
    "LBN": "LEBANON",
    "LSO": "LESOTHO",
    "LBR": "LIBERIA",
    "LBY": "LIBYA",
    "LIE": "LIECHTENSTEIN",
    "LTU": "LITHUANIA",
    "LUX": "LUXEMBOURG",
    "MAC": "MACAO",
    "MKD": "MACEDONIA",
    "MDG": "MADAGASCAR",
    "MWI": "MALAWI",
    "MYS": "MALAYSIA",
    "MDV": "MALDIVES",
    "MLI": "MALI",
    "MLT": "MALTA",
    "MHL": "MARSHALL ISLANDS",
    "MTQ": "MARTINIQUE",
    "MRT": "MAURITANIA",
    "MUS": "MAURITIUS",
    "FSM": "MICRONESIA - FEDERATED STATES OF",
    "MDA": "MOLDOVA - REPUBLIC OF",
    "MCO": "MONACO",
    "MNG": "MONGOLIA",
    "MNE": "MONTENEGRO",
    "MSR": "MONTSERRAT",
    "MAR": "MOROCCO",
    "MOZ": "MOZAMBIQUE",
    "MMR": "MYANMAR",
    "NAM": "NAMIBIA",
    "NRU": "NAURU",
    "NPL": "NEPAL",
    "NLD": "NETHERLANDS",
    "ANT": "NETHERLANDS ANTILLES",
    "NCL": "NEW CALEDONIA",
    "NZL": "NEW ZEALAND",
    "NIC": "NICARAGUA",
    "NER": "NIGER",
    "NGA": "NIGERIA",
    "NIU": "NIUE",
    "NOR": "NORWAY",
    "OMN": "OMAN",
    "PAK": "PAKISTAN",
    "PLW": "PALAU",
    "PAN": "PANAMA",
    "PNG": "PAPUA NEW GUINEA",
    "PRY": "PARAGUAY",
    "PER": "PERU",
    "PHL": "PHILIPPINES",
    "PCN": "PITCAIRN",
    "POL": "POLAND",
    "PRT": "PORTUGAL",
    "QAT": "QATAR",
    "ROU": "ROMANIA",
    "RUS": "RUSSIAN FEDERATION",
    "RWA": "RWANDA",
    "BLM": "SAINT BARTHELEMY",
    "KNA": "SAINT KITTS AND NEVIS",
    "LCA": "SAINT LUCIA",
    "MAF": "SAINT MARTIN",
    "VCT": "SAINT VINCENT AND THE GRENADINES",
    "WSM": "SAMOA",
    "SMR": "SAN MARINO",
    "STP": "SAO TOME AND PRINCIPE",
    "SAU": "SAUDI ARABIA",
    "SEN": "SENEGAL",
    "SRB": "SERBIA",
    "SCG": "SERBIA AND MONTENEGRO",
    "SYC": "SEYCHELLES",
    "SLE": "SIERRA LEONE",
    "SGP": "SINGAPORE",
    "SXM": "SINT MAARTEN",
    "SVK": "SLOVAKIA (Slovak Republic)",
    "SVN": "SLOVENIA",
    "SLB": "SOLOMON ISLANDS",
    "SOM": "SOMALIA",
    "ZAF": "SOUTH AFRICA",
    "SSD": "SOUTH SUDAN",
    "ESP": "SPAIN",
    "LKA": "SRI LANKA",
    "SDN": "SUDAN",
    "SUR": "SURINAME",
    "SWZ": "SWAZILAND",
    "SWE": "SWEDEN",
    "CHE": "SWITZERLAND",
    "SYR": "SYRIA",
    "TWN": "TAIWAN",
    "TJK": "TAJIKISTAN",
    "TZA": "TANZANIA - UNITED REPUBLIC OF",
    "THA": "THAILAND",
    "TLS": "TIMOR-LESTE",
    "TGO": "TOGO",
    "TON": "TONGA",
    "TTO": "TRINIDAD AND TOBAGO",
    "TUN": "TUNISIA",
    "TUR": "TÜRKIYE - REPUBLIC OF",
    "TKM": "TURKMENISTAN",
    "TCA": "TURKS AND CAICOS ISLANDS",
    "TUV": "TUVALU",
    "UGA": "UGANDA",
    "UKR": "UKRAINE",
    "COM": "UNION OF THE COMOROS",
    "ARE": "UNITED ARAB EMIRATES",
    "GBR": "UNITED KINGDOM",
    "URY": "URUGUAY",
    "SUN": "USSR",
    "UZB": "UZBEKISTAN",
    "VUT": "VANUATU",
    "VAT": "VATICAN CITY STATE (HOLY SEE)",
    "VEN": "VENEZUELA",
    "VNM": "VIETNAM",
    "PSE": "WEST BANK AND GAZA",
    "YEM": "YEMEN",
    "YUG": "YUGOSLAVIA",
    "ZAR": "ZAIRE",
    "ZMB": "ZAMBIA",
    "ZWE": "ZIMBABWE"
}
const getRecorderName = () => `Recorder_${moment().unix()}.mp4`
const us_passport_form = async (order, task, data) => {
    const { page, browser } = await connect({
        args: ["--start-maximized"],
        turnstile: true,
        headless: false,
        customConfig: {},
        connectOption: {
            defaultViewport: null
        },
        plugins: []
    });
    const recorder = new PuppeteerScreenRecorder(page);
    const recorderName = getRecorderName();
    await recorder.start(recorderName);

    let start_time = moment().unix()

    fs.existsSync('images') && fs.rmSync('images', { recursive: true })
    fs.mkdirSync('images', { recursive: true })

    const btAPI = new BTAPI();
    let ad_email = await btAPI.randomEmail("entry_test")
    page.setDefaultNavigationTimeout(60000); // Increase timeout to 2 minutes
    page.setDefaultTimeout(60000);
    try {
        await page.goto('https://secure.login.gov/sign_up/enter_email');

        await page.waitForSelector('[name="user[email]"]', { delay: 200 })
        await page.type('[name="user[email]"]', ad_email, { delay: 200 })
        await page.evaluate(() => {
            document.querySelector('#user_email_language_en').click()
            document.querySelector('#user_terms_accepted').click()
            document.querySelector('button[type="submit"]').click()
        })

        await sleep(10000)
        let selected_email = null
        while (moment().unix() > start_time && moment().unix() - start_time < 120) {
            const emails = await btAPI.getEmails(ad_email)
            if (emails?.length > 0 && emails[0].time > start_time) {
                selected_email = emails[0]
                break
            }
            await new Promise(r => setTimeout(r, 3000))
        }
        const regex = /(?:https?:\/\/)[^\s)\n]+/g;
        const urls = selected_email.body.match(regex);

        await page.goto(urls[0])
        await new Promise(r => setTimeout(r, 3000))
        await page.waitForSelector('.password-confirmation__input')
        await page.type('.password-confirmation__input', 'Admin@AriaDirect')
        await page.type('.password-confirmation__input-confirmation', 'Admin@AriaDirect')
        await page.click('[type="submit"]')

        await new Promise(r => setTimeout(r, 3000))
        await page.waitForSelector('#two_factor_options_form_selection_backup_code')
        await page.evaluate(() => {
            document.querySelector('#two_factor_options_form_selection_backup_code').click()
            document.querySelector('[type="submit"]').click()
        })
        await sleep(3000)

        const codes = await page.evaluate(() => {
            const codeElements = document.querySelectorAll('code.text-bold.h2');
            return Array.from(codeElements).map(element =>
                element.textContent.trim()
            );
        });
        await page.evaluate(() => {
            document.querySelector('#backup_code_accepted_form_backup_code_notice_accepted').click()
            document.querySelector('[type="submit"]').click()
        })
        await sleep(10000)
        await page.evaluate(() => {
            document.querySelector('[type="submit"]').click()
        })

        console.log(ad_email)
        console.log(codes)

        await page.goto(`https://ttp.cbp.dhs.gov/`)
        await sleep(10000)
        await page.click(`::-p-xpath(/html/body/go-app/div/go-home/div[2]/div/div/div/div[1]/div[2]/div/div[5]/a[2])`)
        await sleep(2000)
        await page.click(`a ::-p-text(Continue with your Global Entry Application)`)
        await sleep(2000)
        await page.click(`button ::-p-text(CONSENT & CONTINUE)`)
        await sleep(5000)
        //Agree and continue
        await page.click(`button ::-p-text(Agree and continue)`)
        await sleep(5000)
        // //Login
        // {
        //     await page.type('#user_email', '<EMAIL>')
        //     await page.type('[name="user[password]"]', 'Admin@AriaDirect')
        //     await page.click(`button ::-p-text(Submit)`)
        //     await sleep(5000)
        //     await page.type('[name="backup_code_verification_form[backup_code]"]', 'V9PZ58M2Y6AW')
        //     await page.click(`button ::-p-text(Submit)`)
        //     await sleep(10000)
        // }
        buttonExists = await page.$$eval('button', (buttons) => {

            return buttons.some(button => button.textContent.includes('Continue Application'));
        });
        if (!buttonExists) {
            //Base Info
            {
                await page.type('[id="firstName"]', data.passport_core_info_given_name)
                await page.type('[id="lastName"]', data.passport_core_info_surname)

                //Date of Birth
                {
                    allOptionValues = await page.$$eval('#DOB_month option', options =>
                        options.map(opt => opt.value)
                    );
                    valueToSelect = allOptionValues.find(val => val.endsWith(`: ${moment(data.passport_core_info_date_of_birth).format("MM")}`));
                    await page.select('#DOB_month', valueToSelect);

                    await page.type('[id="DOB_day"]', moment(data.passport_core_info_date_of_birth).format("DD"))
                    await page.type('[id="DOB_year"]', moment(data.passport_core_info_date_of_birth).format("YYYY"))
                }

                //Country Of Birth
                {
                    allOptionValues = await page.$$eval('#countryOfBirth option', options =>
                        options.map(opt => opt.value)
                    );
                    valueToSelect = allOptionValues.find(val => val.endsWith(`: ${get_country_iso2(data.passport_core_info_country_of_birth)}`));
                    await page.select('#countryOfBirth', valueToSelect);
                }

                //S
                {
                    allOptionValues = await page.$$eval('#stateOfBirth option', options =>
                        options.map(opt => opt.value)
                    );
                    valueToSelect = allOptionValues.find(val => val.endsWith(`: ${data.passport_core_info_state_of_birth}`));
                    await page.select('#stateOfBirth', valueToSelect);
                }
                await page.type('[id="cityOfBirth"]', data.passport_core_info_city_of_birth)

                await page.type('[id="emailAddress"]', ad_email)
                await page.type('[id="emailConfirmation"]', ad_email)

                await page.select('#phonetype_0', "C")
                await page.select('#phoneformat_0', "N") //Phone Format (N : United States, Canada, +1, M : Mexico , I : Internaional)
                await page.type('#phoneNumber_0', PhoneNumber(data.personal_core_info_phone.phone).nationalNumber ?? "")

                if (data.additional_question_background_question_ge_question_6) {
                    await page.click('#appliedTPPYes')
                    await sleep(500)
                    await page.click('#appToggleTPP')
                    await sleep(500)
                    await page.type("#passId", data.additional_question_background_question_membership_id)
                } else {
                    await page.click('#appliedTPPNo')
                }

                // ge_question_7 - Have you ever applied for United States/Mexico FAST or United States/Canada FAST?
                if (data.additional_question_background_question_ge_question_7) {
                    await page.click('#appliedFastYes')
                    if (task?.type != "renew") {
                        await page.click('#appToggleFast')
                        await page.type("#fastId", data.additional_question_background_question_fast_id)
                    }
                } else {
                    await page.click('#appliedFastNo')
                }

                await page.click(`button ::-p-text(SAVE)`)
                await sleep(2000)

                if (task?.type == "renew") {
                    await page.click(`button ::-p-text(CONFIRM)`)
                }
                await sleep(5000)
                await page.click(`button ::-p-text(Continue)`)
                await sleep(2000)
                //*Are you a citizen of the United States? 
                await page.click('#areCitizenYes')
                //Apply for Global Entry, NEXUS, SENTRI
                await page.click('#tppOption')

                await page.click('#globalEntry')
                await page.click('#imminentIntlTravelNo')
                //included APEC ? 
                if (order.service.tag.includes('USAPEC')) {
                    await page.click('#apec')
                }

                await page.click(`button ::-p-text(NEXT)`)
                await sleep(2000)
                await page.click(`a ::-p-text(Continue with your Global Entry Application)`)
                await sleep(5000)

                await page.select('#marketingQuestion', '6: Media')
                await page.click(`button ::-p-text(Apply for Global Entry)`)
                await sleep(2000)
            }
        } else {
            await page.click('button ::-p-text(Continue Application)');
            await sleep(5000);
        }

        //Personal Information
        {
            await page.click(`${{ "M": "#maleGender", "F": "#femaleGender" }[data.passport_core_info_gender]}`)
            await page.select('#eyeColor', {
                'brown': 'BR',
                'blue': 'BL',
                'green': 'GR',
                'hazel': 'HA',
                'gray': 'GY',
                'black': 'BK',
            }[data.personal_core_info_eye_color] ?? 'BR');

            const heightRegex = /(\d+)ft\.(\d+)in\./;
            const heightMatch = data.personal_core_info_height.match(heightRegex);
            if (heightMatch) {
                const feet = heightMatch[1];
                const inches = heightMatch[2];
                await page.locator('[name="heightFeet"]').fill(feet);
                await page.locator('[name="heightInches"]').fill(inches);
            }

            if (data.personal_core_info_have_used_other_name_before) {
                await page.click('#aliasesYes')
                await page.type('#aliasFirstName_0', data.personal_core_info_other_given_name)
                await page.type('#aliasLastName_0', data.personal_core_info_other_surname)
            } else {
                await page.click('#aliasesNo')
            }
            //Do you have a TSA PreCheck® Known Traveler Number (KTN)?
            await page.click('#tsaKtnsNo')

        }
        await page.click(`button ::-p-text(SAVE & CONTINUE)`)
        await sleep(10000)

        //Documents
        {
            await page.select('#ddlDocType', "PT")
            await page.click(`button ::-p-text(Add)`)
            await sleep(1000)
            await page.click("#notifyBtn")
            await page.type('[name="txtLastName"]', data.passport_core_info_surname)
            await page.type('[name="txtGivenName"]', data.passport_core_info_given_name)
            //Date of Birth
            {
                allOptionValues = await page.$$eval('#dateOfBirthMonth0_C option', options =>
                    options.map(opt => opt.value)
                );
                valueToSelect = allOptionValues.find(val => val.endsWith(`: ${moment(data.passport_core_info_date_of_birth).format("MM")}`));
                await page.select('#dateOfBirthMonth0_C', valueToSelect);

                await page.type('[name="dateOfBirthDay"]', moment(data.passport_core_info_date_of_birth).format("DD"))
                await page.type('[name="dateOfBirthYear"]', moment(data.passport_core_info_date_of_birth).format("YYYY"))
            }

            await page.type('[name="txtDocNumber"]', data.passport_core_info_passport_number)

            //Date of Issue
            {
                allOptionValues = await page.$$eval('#dateOfIssuanceMonth0_C option', options =>
                    options.map(opt => opt.value)
                );
                valueToSelect = allOptionValues.find(val => val.endsWith(`: ${moment(data.passport_core_info_issue_date).format("MM")}`));
                await page.select('#dateOfIssuanceMonth0_C', valueToSelect);

                await page.type('[name="dateOfIssuanceDay"]', moment(data.passport_core_info_issue_date).format("DD"))
                await page.type('[name="dateOfIssuanceYear"]', moment(data.passport_core_info_issue_date).format("YYYY"))
            }

            //Date of Expiration
            {
                allOptionValues = await page.$$eval('#expirationMonth0_C option', options =>
                    options.map(opt => opt.value)
                );
                valueToSelect = allOptionValues.find(val => val.endsWith(`: ${moment(data.passport_core_info_expiration_date).format("MM")}`));
                await page.select('#expirationMonth0_C', valueToSelect);

                await page.type('[name="expirationDay"]', moment(data.passport_core_info_expiration_date).format("DD"))
                await page.type('[name="expirationYear"]', moment(data.passport_core_info_expiration_date).format("YYYY"))
            }

        }
        await page.click(`button ::-p-text(SAVE & CONTINUE)`)
        await sleep(3000)
        buttonExists = await page.$$eval('button', (buttons) => {

            return buttons.some(button => button.textContent.includes('OK'));
        });
        if (buttonExists) {
            await page.click(`button ::-p-text(OK)`)
            await sleep(2000)
            await page.click('#bypassAccepted0_A')
            await sleep(2000)
            await page.click(`button ::-p-text(SAVE & CONTINUE)`)
        }
        await sleep(10000)

        //Driver License
        {
            if (data.document_copy_of_document_do_you_hold_driver_license) {
                await page.click('#haveLicenseYes')
                //TODO Define Pod
                await page.type('#licenseNumber', data.personal_driver_license_driver_license_number)

                //Country Of Issuance
                {
                    allOptionValues = await page.$$eval('#countryOfIssuance option', options =>
                        options.map(opt => opt.value)
                    );
                    valueToSelect = allOptionValues.find(val => val.endsWith(`: US`));
                    await page.select('#countryOfIssuance', valueToSelect);
                }
                //State Of Issuance
                {
                    allOptionValues = await page.$$eval('#stateOfIssuance option', options =>
                        options.map(opt => opt.value)
                    );
                    valueToSelect = allOptionValues.find(val => val.endsWith(`: ${data.personal_driver_license_state_of_issuance}`));
                    await page.select('#stateOfIssuance', valueToSelect);
                }
                //Date of Expiration
                {
                    allOptionValues = await page.$$eval('#DLE_month option', options =>
                        options.map(opt => opt.value)
                    );
                    valueToSelect = allOptionValues.find(val => val.endsWith(`: ${moment(data.personal_driver_license_expiration_date).format("MM")}`));
                    await page.select('#DLE_month', valueToSelect);

                    await page.type('#DLE_day', moment(data.personal_driver_license_expiration_date).format("DD"))
                    await page.type('#DLE_year', moment(data.personal_driver_license_expiration_date).format("YYYY"))
                }
                await page.type('[id="firstName"]', data.passport_core_info_given_name)
                await page.type('[id="lastName"]', data.passport_core_info_surname)

                //Date of Birth
                {
                    allOptionValues = await page.$$eval('#DLDOB_month option', options =>
                        options.map(opt => opt.value)
                    );
                    valueToSelect = allOptionValues.find(val => val.endsWith(`: ${moment(data.passport_core_info_date_of_birth).format("MM")}`));
                    await page.select('#DLDOB_month', valueToSelect);

                    await page.type('[id="DLDOB_day"]', moment(data.passport_core_info_date_of_birth).format("DD"))
                    await page.type('[id="DLDOB_year"]', moment(data.passport_core_info_date_of_birth).format("YYYY"))
                }
                //Is this an enhanced driver's license (EDL)?
                //Is this a commercial driver's license (CDL)?
                await page.click('#isEDLNo')
                await page.click('#isCDLNo')
            } else {
                await page.click('#haveLicenseNo')
            }
        }
        await page.click(`button ::-p-text(SAVE & CONTINUE)`)
        await sleep(10000)


        //Vehicle Information
        if (data.additional_question_background_question_ge_question_8) {
            await page.click('#driveBorderYes')
        } else {
            await page.click('#driveBorderNo')
        }

        await page.click(`button ::-p-text(SAVE & CONTINUE)`)
        await sleep(10000)

        //Address Information
        const addresses = data.address_address_info_addresses;
        for (let i = addresses.length - 1; i >= 0; i--) {
            index = addresses.length - i - 1
            if (index > 0) {
                await page.click(`button ::-p-text(Add Previous Address)`, { delay: 100, count: 1 });
                await sleep(10000)
            }
            address = addresses[i];

            //Work From
            const liveFrom = address.find(info => info.id === 'address_address_info_start_from').value.fe
            const [month, year] = liveFrom.split('/');
            await page.type(`[id="startYear_${index}"]`, year);
            {
                allOptionValues = await page.$$eval(`#startMonth_${index} option`, options =>
                    options.map(opt => opt.value)
                );
                valueToSelect = allOptionValues.find(val => val.endsWith(`: ${month}`));
                await page.select(`#startMonth_${index}`, valueToSelect);
            }
            if (index > 0) {
                //Work To
                const liveFrom = address.find(info => info.id === 'address_address_info_end_to').value.fe
                const [month, year] = liveFrom.split('/');
                await page.type(`[id="endYear_${index}"]`, year);
                {
                    allOptionValues = await page.$$eval(`#endMonth_${index} option`, options =>
                        options.map(opt => opt.value)
                    );
                    valueToSelect = allOptionValues.find(val => val.endsWith(`: ${month}`));
                    await page.select(`#endMonth_${index}`, valueToSelect);
                }
            }
            //Country 
            {
                allOptionValues = await page.$$eval(`#country_${index} option`, options =>
                    options.map(opt => opt.value)
                );
                valueToSelect = allOptionValues.find(val => val.endsWith(`: ${get_country_iso2(address.find(info => info.id === 'address_address_info_country').value.fe)}`));
                await page.select(`#country_${index}`, valueToSelect);
            }
            await page.type(`#addressLine1_${index}`, address.find(info => info.id === 'address_address_info_address').value.fe)
            await page.type(`#city_${index}`, address.find(info => info.id === 'address_address_info_city').value.fe)

            //State Of Issuance
            {
                allOptionValues = await page.$$eval(`#state_${index} option`, options =>
                    options.map(opt => opt.value)
                );
                valueToSelect = allOptionValues.find(val => val.endsWith(`: ${address.find(info => info.id === 'address_address_info_state').value.fe}`));
                await page.select(`#state_${index}`, valueToSelect);
            }
            await page.type(`#zip_${index}`, address.find(info => info.id === 'address_address_info_zip_code').value.fe)

            sleep(3000)
        }
        await sleep(5000)
        //Mailing Address
        if (data.address_mailing_address_is_mailing_address_same_current_address) {
            await page.click('#mailingEqCurrentYes2')
        } else {
            const liveFrom = moment().subtract(1, 'month').format('MM/YYYY');
            const [month, year] = liveFrom.split('/');
            await page.click('#mailingEqCurrentNo2')
            //Country
            {
                allOptionValues = await page.$$eval('#m_country option', options =>
                    options.map(opt => opt.value)
                );
                valueToSelect = allOptionValues.find(val => val.endsWith(`: ${get_country_iso2(data.address_mailing_address_country)}`));
                await page.select('#m_country', valueToSelect);
            }
            //State
            {
                allOptionValues = await page.$$eval('#m_state option', options =>
                    options.map(opt => opt.value)
                );
                valueToSelect = allOptionValues.find(val => val.endsWith(`: ${data.address_mailing_address_state}`));
                await page.select('#m_state', valueToSelect);
            }
            await page.type('#m_addressLine1', data.address_mailing_address_address)
            await page.type('#m_city', data.address_mailing_address_city)
            await page.type('#m_zip', data.address_mailing_address_zip_code)

            await page.type(`[name="m_startYear"]`, year);
            {
                allOptionValues = await page.$$eval('[name="m_startMonth"] option', options =>
                    options.map(opt => opt.value)
                );
                valueToSelect = allOptionValues.find(val => val.endsWith(`: ${month}`));
                await page.select(`[name="m_startMonth"]`, valueToSelect);
            }
        }
        await page.click(`button ::-p-text(SAVE & CONTINUE)`)
        await sleep(1000)
        await page.click("#notifyBtn")
        await sleep(1000)
        await page.click(`button ::-p-text(SAVE & CONTINUE)`)
        await sleep(10000)


        //Employment Information
        //add first
        await page.click(`button ::-p-text(Add Employment)`, { delay: 100, count: 1 });
        const employmentInfos = data.employment_core_info_employment_info;
        for (let i = employmentInfos.length - 1; i >= 0; i--) {
            index = employmentInfos.length - i - 1
            employment = employmentInfos[i];
            if (index > 0) {
                await page.click(`button ::-p-text(Add Employment)`, { delay: 100, count: 1 });
                await sleep(5000)
            }

            const employmentStatus = employment.find(info => info.id === 'employment_core_info_status').value.fe
            await page.select(`#status${index}`, {
                'employed': 'E',
                'self_employed': 'F',
                'retired': 'R',
                'student': 'S',
                'unemployed': 'U',
                'housewife': 'U',
            }[employmentStatus]);

            const employmentInfo = employment.find(info => info.id === 'employment_core_info_status').option_choice[employmentStatus]
            //Work From
            const workTime = employmentInfo.find(info => info.id === 'employment_core_info_when_job_begin').value.fe
            const [month, year] = workTime.split('/');
            {
                allOptionValues = await page.$$eval(`#startMonth${index} option`, options =>
                    options.map(opt => opt.value)
                );
                valueToSelect = allOptionValues.find(val => val.endsWith(`: ${month}`));
                await page.select(`#startMonth${index}`, valueToSelect);
            }
            await page.type(`#startYear${index}`, year);
            if (index > 0) {
                await page.click(`#current${index}`)
                await sleep(1000)
                //Work To
                const workTime = employmentInfo.find(info => info.id === 'employment_core_info_end_to').value.fe
                const [month, year] = workTime.split('/');
                {
                    selector = `#endYear${index}[name="endMonth"]`;
                    allOptionValues = await page.$$eval(`${selector} option`, options =>
                        options.map(opt => opt.value)
                    );
                    valueToSelect = allOptionValues.find(val => val.endsWith(`: ${month}`));
                    await page.select(selector, valueToSelect);
                }
                await page.type(`[id="endYear${index}"][name="endYear"]`, year);
            }
            await page.type(`#occupation${index}`, employmentInfo.find(info => info.id === 'employment_core_info_occupation').value.fe)
            await page.type(`#employer${index}`, employmentInfo.find(info => info.id === 'employment_company_address_company_name').value.fe)
            const phone = PhoneNumber(employmentInfo.find(info => info.id === 'employment_company_address_phone').value.fe.phone);
            //No Require if not current job
            if (index == 0) {
                if (phone.countryCallingCode === '1') {
                    //US, Canada
                    await page.select(`#phoneFormat${index}`, "N")
                } else if (phone.countryCallingCode === '52') {
                    //Mexico
                    await page.select(`#phoneFormat${index}`, "M")
                } else {
                    //International
                    await page.select(`#phoneFormat${index}`, "I")
                    await page.type(`#countryCode${index}`, phone.countryCallingCode ?? "")
                }

                await page.type(`#phoneNumber${index}`, phone.nationalNumber ?? "")
            }
            //Country
            {
                allOptionValues = await page.$$eval(`#country${index} option`, options =>
                    options.map(opt => opt.value)
                );
                valueToSelect = allOptionValues.find(val => val.endsWith(`: ${get_country_iso2(employmentInfo.find(info => info.id === 'employment_company_address_country').value.fe)}`));
                await page.select(`#country${index}`, valueToSelect);
            }
            //State
            {
                allOptionValues = await page.$$eval(`#state${index} option`, options =>
                    options.map(opt => opt.value)
                );
                valueToSelect = allOptionValues.find(val => val.endsWith(`: ${employmentInfo.find(info => info.id === 'employment_company_address_state').value.fe}`));
                await page.select(`#state${index}`, valueToSelect);
            }
            await page.type(`#city${index}`, employmentInfo.find(info => info.id === 'employment_company_address_city').value.fe)
            await page.type(`#addressLine1${index}`, employmentInfo.find(info => info.id === 'employment_company_address_address').value.fe)
            await page.type(`#zip${index}`, employmentInfo.find(info => info.id === 'employment_company_address_zip_code').value.fe)
        }
        await page.click(`button ::-p-text(SAVE & CONTINUE)`)
        await sleep(1000)
        await page.click("#notifyBtn")
        await sleep(1000)
        await page.click(`button ::-p-text(SAVE & CONTINUE)`)
        await sleep(10000)


        //Travel History
        if (data.personal_travel_history_ge_have_travel_in_last_5y.length > 0) {
            await page.click('#haveYouTraveledYes')
            for (const [index, country] of data.personal_travel_history_ge_have_travel_in_last_5y.entries()) {
                await page.click(`button ::-p-text(${countries[country]})`, { delay: 100, count: 1 });
            }
        } else {
            await page.click('#haveYouTraveledNo')
        }
        await page.click(`button ::-p-text(SAVE & CONTINUE)`)
        await sleep(10000)

        //Background Questions
        if (data.additional_question_background_question_us_airport_background_6) {
            await page.click('#question_0_yes')
        } else {
            await page.click('#question_0_no')
        }
        if (data.additional_question_background_question_us_airport_background_3) {
            await page.click('#question_1_yes')
        } else {
            await page.click('#question_1_no')
        }
        if (data.additional_question_background_question_us_airport_background_4) {
            await page.click('#question_2_yes')
        } else {
            await page.click('#question_2_no')
        }
        if (data.additional_question_background_question_us_airport_background_5) {
            await page.click('#question_3_yes')
        } else {
            await page.click('#question_3_no')
        }
        await page.click(`button ::-p-text(SAVE & CONTINUE)`)
        await sleep(10000)

        await recorder.stop();
        await browser.close()
        return {
            note: [
                "Username: " + ad_email,
                "Password: Admin@AriaDirect",
                "Codes: " + codes.join(' | '),
                "Apply: " + moment().format('DD/MM/YYYY'),
                "Tracking URL: https://ttp.cbp.dhs.gov"
            ].join(",\n"),
            recorderName: recorderName
        }
    } catch (error) {
        console.log(error)
        console.log(error.message)
        await recorder.stop();
        await browser.close()
        return null
    }

}


const MAX_RETRY = 1;
const create_form = async ({ pod_data, order, task }) => {
    console.log(JSON.stringify(pod_data, null, 2))
    let form = null
    for (let i = 0; i < MAX_RETRY; i++) {
        form = await us_passport_form(order, task, pod_data)
        if (form != null) {
            break
        }
    }
    const result = {
        success: false,
        form_callback: {}
    }

    const buckets = JSON.parse(process.env.ad_s3)
    if (form != null) {
        result.form_callback = form
        result.success = true
    }

    let recorderName = null;
    if (form && form.recorderName) {
        recorderName = form.recorderName;
        await s3_upload_buff(buckets.ariadirect_prod_applications, `steps/${recorderName}`, fs.readFileSync(recorderName))
        fs.rmSync(recorderName, { recursive: true })
        result.form_callback.steps = `https://${buckets.ariadirect_prod_applications}.s3.amazonaws.com/steps/${recorderName}`
    }

    fs.rmSync('images', { recursive: true })
    console.log("STEPS:", result.form_callback.steps)
    return result
}

const main = async (order_id, task_id) => {
    try {
        const { order, task, pod_data } = await get_order_task_pod_data(order_id, task_id)
        const data = await create_form({ pod_data, order, task })
        console.log(data)
        return data
    } catch (error) {
        console.log(error)
    }
}
module.exports = { main, create_form }