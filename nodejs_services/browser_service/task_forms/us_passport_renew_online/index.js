const fs = require('fs');
const axios = require('axios');
const uuid = require('uuid');
const moment = require('moment');
const { connect } = require('puppeteer-real-browser');
const puppeteer = require('puppeteer');
const { PuppeteerScreenRecorder } = require('puppeteer-screen-recorder');
const { PDFDocument } = require('pdf-lib');
const jimp = require('jimp');
const tmp = require('tmp');
const { STATE } = require('./data_mapping');
const { exec } = require('child_process');
const util = require('util');
const execPromise = util.promisify(exec);
const os = require('os');
const path = require('path');
const BTAPI = require('../../shared/bt_api');
const { s3_upload_buff, get_order_task_pod_data, sleep } = require('../../shared/helpers');
const { get_image_captcha_text } = require('../../shared/captcha');

const getRecorderName = () => `Recorder_${moment().unix()}.mp4`
let DEBUG = false
const us_passport_form = async (order, task, data) => {
    const { page, browser } = await connect({
        headless: false,
        turnstile: false,
        disableXvfb: DEBUG,
        customConfig: {
            ignoreDefaultArgs: ['--enable-automation'],  // Disable automation flag
        },
        connectOption: {
            defaultViewport: {
                width: 1920,
                height: 1080
            },
            args: [
            ],
            ignoreHTTPSErrors: true,
            waitForInitialPage: true,
        },
    });
    const recorder = new PuppeteerScreenRecorder(page);
    const recorderName = getRecorderName();
    await recorder.start(recorderName);

    // !DEBUG && await page.setViewport({ width: 1000, height: 1500 });
    page.setDefaultNavigationTimeout(60000); // Increase timeout to 2 minutes
    page.setDefaultTimeout(60000);
    page.setUserAgent('Mozilla/5.0 (Windows NT 10.0; Win64; x64) Chrome/120.0.0.0 Safari/537.36');


    let start_time = moment().unix()

    fs.existsSync('images') && fs.rmSync('images', { recursive: true })
    fs.mkdirSync('images', { recursive: true })

    const btAPI = new BTAPI();
    let ad_email = await btAPI.randomEmail("uspp")

    try {
        await page.goto('https://secure.login.gov/sign_up/enter_email');

        await page.waitForSelector('[name="user[email]"]', { delay: 200 })
        await page.type('[name="user[email]"]', ad_email, { delay: 200 })
        await page.evaluate(() => {
            document.querySelector('#user_email_language_en').click()
            document.querySelector('#user_terms_accepted').click()
            document.querySelector('button[type="submit"]').click()
        })

        await sleep(10000)
        let selected_email = null
        while (moment().unix() > start_time && moment().unix() - start_time < 120) {
            const emails = await btAPI.getEmails(ad_email)
            if (emails?.length > 0 && emails[0].time > start_time) {
                selected_email = emails[0]
                break
            }
            await new Promise(r => setTimeout(r, 3000))
        }
        const regex = /(?:https?:\/\/)[^\s)\n]+/g;
        const urls = selected_email.body.match(regex);

        await page.goto(urls[0])
        await new Promise(r => setTimeout(r, 3000))
        await page.waitForSelector('.password-confirmation__input')
        await page.type('.password-confirmation__input', 'Admin@AriaDirect')
        await page.type('.password-confirmation__input-confirmation', 'Admin@AriaDirect')
        await page.click('[type="submit"]')

        await new Promise(r => setTimeout(r, 3000))
        await page.waitForSelector('#two_factor_options_form_selection_backup_code')
        await page.evaluate(() => {
            document.querySelector('#two_factor_options_form_selection_backup_code').click()
            document.querySelector('[type="submit"]').click()
        })
        await sleep(3000)

        const codes = await page.evaluate(() => {
            const codeElements = document.querySelectorAll('code.text-bold.h2');
            return Array.from(codeElements).map(element =>
                element.textContent.trim()
            );
        });
        await page.evaluate(() => {
            document.querySelector('#backup_code_accepted_form_backup_code_notice_accepted').click()
            document.querySelector('[type="submit"]').click()
        })
        await sleep(10000)
        await page.evaluate(() => {
            document.querySelector('[type="submit"]').click()
        })

        console.log(ad_email)
        console.log(codes)

        await page.goto(`https://mytravel.state.gov/s/`)
        await sleep(10000)
        await page.click('[title="Go to Online Passport Renewal application"]')
        await sleep(10000)
        await page.evaluate(() => {
            document.querySelector('[type="submit"]').click()
        })
        await sleep(15000)
        await page.waitForSelector('[data-id="welcome-mat-button"]')
        await page.click('[data-id="welcome-mat-button"]')
        await sleep(10000)

        await page.waitForSelector('[name="PAS_Confimration_Check"]')
        await sleep(10000)
        await page.$eval('[name="PAS_Confimration_Check"]', elem => elem.click());
        await page.$eval('[part="button"]', elem => elem.click());
        await sleep(10000)

        await page.type('[name="FirstName_New_User"]', data.passport_core_info_given_name)
        await page.type('[name="LastName_New_User"]', data.passport_core_info_surname)
        await sleep(5000)


        await page.$eval('[part="button"]', elem => elem.click());
        await sleep(10000) // Personal Information has been updated!
        await page.$eval('[part="button"]', elem => elem.click());
        await sleep(10000) // Eligible U.S. citizens can now renew their passports online. Click Start below to begin your application.
        await page.$eval('[part="button"]', elem => elem.click());
        await sleep(10000)

        await page.hover('[data-id="scrollableContainerId"]');
        await page.mouse.wheel({ deltaY: 2000 });

        await sleep(10000)
        await page.$eval('span[part="indicator"]', elem => elem.click());
        await page.$eval('[part="button"]', elem => elem.click());
        await sleep(10000)
        let checkboxes;
        checkboxes = await page.$$('[part="indicator"]')
        for (let i = 0; i < checkboxes.length; i++) {
            await checkboxes[i].click()
        }
        await page.$eval('[part="button"][kx-scope="button-brand"]', elem => elem.click());

        await sleep(10000)

        await page.evaluate(() => {
            const birthDateInput = document.querySelector('[name="Step1_3_Date_of_Birth"]');
            const issueDateInput = document.querySelector('[name="Step1_3_Book_Issue_Date"]');

            if (birthDateInput) {
                birthDateInput.value = "";
                birthDateInput.dispatchEvent(new Event('change', { bubbles: true }));
            }

            if (issueDateInput) {
                issueDateInput.value = "";
                issueDateInput.dispatchEvent(new Event('change', { bubbles: true }));
            }
        });

        await sleep(10000)

        await page.click(`label[for^="${{
            'book': 'RADIO-1-',
            'card': 'RADIO-2-',
            'book_card': 'RADIO-0-'
        }[order.service.tag || 'book']}"] span.slds-radio_faux`);

        await page.type('[name="Step1_3_Last_Name"]', data.passport_core_info_surname)
        await page.type('[name="Step1_3_Date_of_Birth"]', moment(data.passport_core_info_date_of_birth).format('MM/DD/YYYY'))

        if (order.service.tag === 'book_card' || order.service.tag === 'book') {
            await page.type('[name="Step1_3_Passport_Book_Number"]', data.passport_most_recent_pp_pp_book_number)
            await page.type('[name="Step1_3_Book_Issue_Date"]', moment(data.passport_most_recent_pp_pp_book_issue_date).format('MM/DD/YYYY'), { delay: 100 })
            await page.keyboard.press('Enter')
        }

        if (order.service.tag === 'book_card' || order.service.tag === 'card') {
            await page.type('[name="Step1_3_Passport_Card_Number"]', data.passport_most_recent_pp_pp_book_number)
            await page.type('[name="Step1_3_Card_Issue_Date"]', moment(data.passport_most_recent_pp_pp_book_issue_date).format('MM/DD/YYYY'), { delay: 100 })
            await page.keyboard.press('Enter')
        }



        await page.$eval('[part="button"][kx-scope="button-brand"]', elem => elem.click());
        await sleep(10000)
        await page.$eval('[part="button"][kx-scope="button-brand"]', elem => elem.click());
        await sleep(10000)
        await page.$eval('[part="button"][kx-scope="button-brand"]', elem => elem.click());
        await sleep(10000)

        await page.type('[name="Step2_1_First_Name"]', data.passport_core_info_given_name)
        let selects;
        selects = await page.$$('[part="select"]')
        await selects[0].select(data.passport_core_info_country_of_birth)
        if (data.passport_core_info_state_of_birth) {
            selects = await page.$$('[part="select"]')
            await selects[1].select(data.passport_core_info_state_of_birth)

        }
        await sleep(2000)
        await page.type('[name="Step2_1_City_of_Birth"]', data.passport_core_info_city_of_birth)
        await page.select('[name="Step2_1_Sex"]', data.passport_core_info_gender === 'M' ? 'Choice_Male_M' : 'Choice_Female_F')
        await page.type('[name="Step2_1_Social_Security_Number"]', data.personal_core_info_social_security_number.replace(/-/g, ''))
        await page.$eval('[part="button"][kx-scope="button-brand"]', elem => elem.click());
        await sleep(10000)

        checkboxes = await page.$$('[part="indicator"]')
        for (let i = 0; i < checkboxes.length; i++) {
            await checkboxes[i].click()
        }
        await page.$eval('[part="button"][kx-scope="button-brand"]', elem => elem.click());
        await sleep(10000)

        const heightRegex = /(\d+)ft\.(\d+)in\./;
        const heightMatch = data.personal_core_info_height.match(heightRegex);
        if (heightMatch) {
            const feet = heightMatch[1];
            const inches = heightMatch[2];
            await page.select('[name="Step2_3_Height_Feet"]', `Choice_Height_${Number(feet)}`);
            await page.select('[name="Step2_3_Height_Inches"]', `Choice_Height_${Number(inches)}`);
        }

        await page.select('[name="Step2_3_Eye_Color"]', {
            'brown': 'Choice_EyeColor_Brown',
            'blue': 'Choice_EyeColor_Blue',
            'green': 'Choice_EyeColor_Green',
            'hazel': 'Choice_EyeColor_Hazel',
            'gray': 'Choice_EyeColor_Gray',
            'black': 'Choice_EyeColor_Black',
            'amber': 'Choice_EyeColor_Amber',
            'violet': 'Choice_EyeColor_Violet',
        }[data.personal_core_info_eye_color] ?? 'Choice_EyeColor_Brown');

        await page.select('[name="Step2_3_Hair_Color"]', {
            'black': 'Choice_EyeColor_Black',
            'blone': 'Choice_HairColor_Blonde',
            'brown': 'Choice_EyeColor_Brown',
            'red': 'Choice_HairColor_Red',
            'gray': 'Choice_EyeColor_Gray',
            'bald': 'Choice_HairColor_Bald',
        }[data.personal_core_info_hair_color] ?? 'Choice_EyeColor_Other');

        // Check if age is <= 5 years old based on date of birth
        const dob = moment(data.passport_core_info_date_of_birth);
        const age = moment().diff(dob, 'years');
        if (age <= 5) {
            await page.type('[name="Step2_3_Occupation"]', 'CHILD');
        } else {
            await page.type('[name="Step2_3_Occupation"]', data.personal_occupation_occupation);
        }

        await page.$eval('[part="button"][kx-scope="button-brand"]', elem => elem.click());
        await sleep(10000)

        await page.$eval('[part="button"][kx-scope="button-brand"]', elem => elem.click());
        await sleep(10000)

        const photoUpload = await page.$('[data-id="file-upload-input"]');
        // data.photo_passport_photo_copy_of_photo = 'https://ad-prodjp-passport-images-ap-northeast-1.s3.ap-northeast-1.amazonaws.com/removebg-passport/0b3f627e-eaa9-4466-a2dd-26772f79dddd.jpg?AWSAccessKeyId=AKIA4NNDWXYAK4N26X6J&Expires=1734756374&Signature=vXjxalu11Z%2FQAHQn39xOmbL6Rpo%3D'
        const photo_resp = await axios.get(data.photo_passport_photo_copy_of_photo, { responseType: 'arraybuffer' });
        const image = await jimp.read(Buffer.from(photo_resp.data));
        const width = image.getWidth();
        const height = image.getHeight();
        const newImage = await new jimp(width * 1.5, height * 1.5, 0xFFFFFFFF);
        await newImage.composite(image, width * 0.25, height * 0.25);
        const image_buff = await newImage.resize(900, jimp.AUTO).getBufferAsync(jimp.MIME_JPEG)
        const tmp_file = tmp.fileSync({ mode: 0o644, prefix: 'temp-', postfix: '.jpeg' });
        fs.writeFileSync(tmp_file.name, image_buff);
        console.log(tmp_file.name)
        await photoUpload.uploadFile(tmp_file.name);
        await sleep(10000)
        await page.$eval('[part="button"][kx-scope="button-brand"]', elem => elem.click());
        await sleep(10000)
        await page.$eval('[part="button"][kx-scope="button-brand"]', elem => elem.click());
        await sleep(10000)

        await page.type('[name="Step2_5_Mailing_Address_Street_1"]', data.personal_mailing_address_address)
        selects = await page.$$('[part="select"]')
        await selects[0].select(data.personal_mailing_address_country)
        await sleep(1000)
        selects = await page.$$('[part="select"]')
        await selects[1].select(data.personal_mailing_address_state)
        await page.type('[name="Step2_5_Mailing_Address_City"]', data.personal_mailing_address_city)
        await page.type('[name="Step2_5_Mailing_Address_Zip_Code_Required"]', data.personal_mailing_address_zip_code)

        checkboxes = await page.$$('[part="indicator"]')
        for (let i = 0; i < checkboxes.length; i++) {
            await checkboxes[i].click()
        }
        await page.select('[name="Step2_5_Country_Code_1"]', 'PicklistChoiceSet_Country_Codes.United States +1')
        await page.type('[name="Step2_5_Primary_Phone_Number"]', data.personal_core_info_phone.phone?.replace('+1', ''))

        await page.select('[name="Step2_5_Country_Code_2"]', 'FlowImplicit__DefaultTextChoiceName')
        await page.$eval('[part="button"][kx-scope="button-brand"]', elem => elem.click());
        await sleep(10000)

        await page.$eval('[part="button"][kx-scope="button-brand"]', elem => elem.click());
        await sleep(10000)

        // Step 2: Mailing address validation
        checkboxes = await page.$$('.slds-radio_faux')
        if (checkboxes.length === 2) {
            await checkboxes[1].click()
        } else {
            await checkboxes[0].click()
        }

        await page.$eval('[part="button"][kx-scope="button-brand"]', elem => elem.click());
        await sleep(10000)

        // Step 2: Your emergency contact (part 6 of 6)
        await page.type('[name="Step2_6_EmergencyContact_First_Name"]', data.personal_emergency_contact_given_name)
        await page.type('[name="Step2_6_EmergencyContact_Last_Name"]', data.personal_emergency_contact_surname)
        await page.select('[name="Step2_6_EmergencyContact_Relationship"]', {
            "spouse": "PicklistChoiceSet_EmergencyContact_Relationship.Spouse",
            "mother": 'PicklistChoiceSet_EmergencyContact_Relationship.Parent',
            "father": 'PicklistChoiceSet_EmergencyContact_Relationship.Parent',
            "children": 'PicklistChoiceSet_EmergencyContact_Relationship.Child',
            "brother": 'PicklistChoiceSet_EmergencyContact_Relationship.Sibling',
            "sister": 'PicklistChoiceSet_EmergencyContact_Relationship.Sibling',
            "grandparent": "PicklistChoiceSet_EmergencyContact_Relationship.Legal Representative",
            "aunt": "PicklistChoiceSet_EmergencyContact_Relationship.Legal Representative",
            "uncle": "PicklistChoiceSet_EmergencyContact_Relationship.Legal Representative",
            "cousin": "PicklistChoiceSet_EmergencyContact_Relationship.Other Relative",
            "nephew": "PicklistChoiceSet_EmergencyContact_Relationship.Other Relative",
            "niece": "PicklistChoiceSet_EmergencyContact_Relationship.Other Relative",
            "siblings_in_law": "PicklistChoiceSet_EmergencyContact_Relationship.Other Relative",
            "friend": "PicklistChoiceSet_EmergencyContact_Relationship.Other Relative",
        }[data.personal_emergency_contact_relationship] ?? 'PicklistChoiceSet_EmergencyContact_Relationship.Other');

        await page.type('[name="Step2_6_EmergencyContact_Address_Text_1"]', data.personal_emergency_contact_address)

        selects = await page.$$('[part="select"]')
        selects[1].select(data.personal_emergency_contact_country)

        await sleep(1000)

        let labels = await page.$$('[part="select-label"]');
        for (const label of labels) {
            const text = await page.evaluate(el => el.textContent, label);
            const attribute_for = await page.evaluate(el => el.getAttribute('for'), label);
            if (text.includes('Country')) {
                await page.select(`#${attribute_for}`, data.personal_emergency_contact_country);
                await sleep(1000)
            }

            if (text.includes('State')) {
                await page.select(`#${attribute_for}`, data.personal_emergency_contact_state);
            }
        }
        await page.type('[name="Step2_6_EmergencyContact_Address_City"]', data.personal_emergency_contact_city)
        await page.type('[name="Step2_6_EmergencyContact_Address_Zip_Code_Required"]', data.personal_emergency_contact_zip_code)

        page.select('[name="Step2_6_Emergency_Country_Code"]', "PicklistChoiceSet_Country_Codes.United States +1")
        await page.type('[name="Step2_6_Emergency_Primary_Phone_Number"]', data.personal_emergency_contact_phone.phone?.replace('+1', ''))

        await page.$eval('[part="button"][kx-scope="button-brand"]', elem => elem.click());
        await sleep(10000)

        // Product Selection
        labels = await page.$$('[part="input-radio"]')
        await Promise.all(labels.map(async label => {
            if ((await page.evaluate(el => el.textContent, label)).includes('Large Passport')) {
                await label.click();
            }
        }));
        await sleep(1000)
        await page.$eval('[part="button"][kx-scope="button-brand"][title="Next"]', elem => elem.click());
        await sleep(10000)

        // Your Product Selections
        await page.$eval('[part="button"][kx-scope="button-brand"][title="Next"]', elem => elem.click());
        await sleep(10000)

        // Final Review
        await page.$eval('[part="button"][kx-scope="button-brand"]', elem => elem.click());
        await sleep(10000)

        // Sign & Pay
        await page.type('[name="Step5_Sign_and_Pay_Current_U_S_Passport_Book_ICN"]', data.passport_most_recent_pp_pp_book_number)
        checkboxes = await page.$$('[part="indicator"]')
        for (let i = 0; i < checkboxes.length; i++) {
            await checkboxes[i].click()
        }
        await page.$eval('[part="button"][kx-scope="button-neutral"]', elem => elem.click());
        await sleep(10000)

        await recorder.stop();
        await browser.close()
        return {
            note: [
                "Username: " + ad_email,
                "Password: Admin@AriaDirect",
                "Codes: " + codes.join(' | '),
                "Apply: " + moment().format('DD/MM/YYYY'),
                "Tracking URL: https://mytravel.state.gov/s/online-passport-renewal"
            ].join(",\n"),
            recorderName: recorderName
        }
    } catch (error) {
        console.log(error)
        console.log(error.message)
        await recorder.stop();
        await browser.close()
        return null
    }

}


const MAX_RETRY = 1;
const create_form = async ({ pod_data, order, task }) => {
    console.log(JSON.stringify(pod_data, null, 2))
    let form = null
    for (let i = 0; i < MAX_RETRY; i++) {
        form = await us_passport_form(order, task, pod_data)
        if (form != null) {
            break
        }
    }
    const result = {
        success: false,
        form_callback: {}
    }

    const buckets = JSON.parse(process.env.ad_s3)
    if (form != null) {
        result.form_callback = form
        result.success = true
    }

    let recorderName = null;
    if (form && form.recorderName) {
        recorderName = form.recorderName;
        await s3_upload_buff(buckets.ariadirect_prod_applications, `steps/${recorderName}`, fs.readFileSync(recorderName))
        fs.rmSync(recorderName, { recursive: true })
        result.form_callback.steps = `https://${buckets.ariadirect_prod_applications}.s3.amazonaws.com/steps/${recorderName}`
    }

    fs.rmSync('images', { recursive: true })
    console.log("STEPS:", result.form_callback.steps)
    return result
}

const main = async (order_id, task_id) => {
    try {
        const { order, task, pod_data } = await get_order_task_pod_data(order_id, task_id)
        const data = await create_form({ pod_data, order, task })
        console.log(data)
        return data
    } catch (error) {
        console.log(error)
    }
}

module.exports = { main, create_form }