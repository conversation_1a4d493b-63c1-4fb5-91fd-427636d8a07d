const express = require('express');
const cors = require('cors');
const AWS = require('aws-sdk');
const fs = require('fs');
const axios = require('axios');
const { Consumer } = require('sqs-consumer');
const {
    update_packer_status,
    send_zalo_message,
    send_zalo_file,
    get_task_name,
    get_order_task,
    get_zalo_group
} = require('./shared/helpers');
const data_mapping = require('./shared/data_mapping');

// Load environment variables
if (fs.existsSync(".env")) {
    require('dotenv').config();
}
console.log(JSON.stringify(process.env, null, 2))
const app = express();

app.use(cors());
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

const sqs_config = JSON.parse(process.env.ad_sqs);
const aws_config = JSON.parse(process.env.ad_aws);

AWS.config.update({
    region: aws_config.region,
});

const SQS_QUEUE_URL = `${sqs_config.url_prefix}/${sqs_config.website_sqs_name}`;

const create_order_form_funcs = {};
const create_task_form_funcs = {};

fs.readdirSync('./task_forms').forEach((file) => {
    const form_name = file.replace('.js', '');
    create_task_form_funcs[form_name] = require(`./task_forms/${file}`);
});

fs.readdirSync('./order_forms').forEach((file) => {
    const form_name = file.replace('.js', '');
    create_order_form_funcs[form_name] = require(`./order_forms/${file}`);
});

const processMessage = async ({ form_name, task_id, order_id }) => {
    const api_base = JSON.parse(process.env.ad_endpoint).api_base_url;
    const web_base = JSON.parse(process.env.ad_endpoint).web_base_url;

    const create_task_form_func = create_task_form_funcs[form_name];
    const create_order_form_func = create_order_form_funcs[form_name];

    if (create_task_form_func) {
        const task = await get_order_task(order_id, task_id);
        console.log(`Processing task form: ${form_name} for order: ${order_id}, task id: ${task_id}`);
        let result = {
            success: false,
            form_name,
            form_file: "",
            form_callback: null,
            task_id,
            order_id,
        }

        try {
            const form_result = await create_task_form_func.main(order_id, task_id);

            if (form_result) {
                form_result.task_id = task_id;
                form_result.form_name = form_name;

                if (form_result) {
                    if (!form_result.form_callback) {
                        form_result.form_callback = {}
                    }
                    await send_zalo_message(get_zalo_group(), [
                        `${form_result.success ? '<b>[SUCCESS]</b>' : '<b>[FAIL]</b>'} Task Form: ${data_mapping.form_name[form_name] || form_name} for ${get_task_name(task.data)} .Order: <b>${order_id}</b>`,
                        Object.keys(form_result.form_callback).map(v => `<b>${v}</b>: ${form_result.form_callback[v]}`).join('\n'),
                        `Chi tiết: ${web_base}/dashboard/orders/detail?application_id=${task_id}&modal=view&order_id=${order_id}&service=ets`
                    ].filter(v => v).join('\n')).catch(console.error);
                }

                if (form_result.form_file) {
                    await send_zalo_file(get_zalo_group(), form_result.form_file).catch(console.error);
                }
                result.form_file = form_result.form_file || '';
                result.form_callback = form_result.form_callback || null;
                result.success = form_result.success === true;
            }
        } catch (error) {
            console.error(error);
        }
        const api_token = JSON.parse(process.env.ad_api_token);
        await update_packer_status(
            api_token.token,
            `${api_base}/v1/packer/internals-packers/${order_id}/update-service-task-form`,
            result,
        );
    }
    if (create_order_form_func) {
        console.log(`Processing order form: ${form_name} for order: ${order_id}`);
        let result = {
            success: false,
            form_name,
            form_file: "",
            form_callback: null,
            task_id,
            order_id,
        }
        try {
            let form_result = await create_order_form_func.main(order_id);
            if (form_result) {
                form_result.form_name = form_name;

                if (form_result.form_file) {
                    await send_zalo_file(get_zalo_group(), form_result.form_file);
                }
                if (form_result) {
                    if (!form_result.form_callback) {
                        form_result.form_callback = {}
                    }
                    await send_zalo_message(get_zalo_group(), [
                        `${form_result.success ? '<b>[SUCCESS]</b>' : '<b>[FAIL]</b>'} Order Form: ${data_mapping.form_name[form_name] || form_name} for order: <b>${order_id}</b>`,
                        Object.keys(form_result.form_callback).map(v => `<b>${v}</b>: ${form_result.form_callback[v]}`).join('\n'),
                        `Chi tiết: ${web_base}/dashboard/orders/detail?order_id=${order_id}&service=ets`,
                    ].filter(v => v).join('\n'));
                }

                result.form_file = form_result.form_file || '';
                result.form_callback = form_result.form_callback || null;
                result.success = form_result.success === true;
            }
        } catch (error) {
            console.error(error);
        }
        const api_token = JSON.parse(process.env.ad_api_token);
        await update_packer_status(
            api_token.token,
            `${api_base}/v1/packer/internals-packers/${order_id}/update-service-order-form`,
            result,
        );
    }
};

// processMessage({
//     form_name: 'us_passport_renew_online',
//     order_id: 7906,
//     task_id: 10724,
// })

const processMessageFormOnly = async ({ form_name }) => {
    const create_order_form_func = create_order_form_funcs[form_name];

    console.log(`Processing order form: ${form_name}`);
    const result = await create_order_form_func();
    console.log(result)
};

const consumer = Consumer.create({
    region: aws_config.region,
    queueUrl: SQS_QUEUE_URL,
    waitTimeSeconds: 1,
    shouldDeleteMessages: true,
    handleMessage: async (message) => {
        const { form_name, task_id, order_id } = JSON.parse(message.Body);
        if (!task_id && !order_id) {
            await processMessageFormOnly({ form_name }).catch(console.error);
        } else {
            await processMessage({ form_name, task_id, order_id }).catch(console.error);
        }
        return true;
    },
});

// SQS Consumer error handling
consumer.on('error', (err) => {
    console.error('Consumer error:', err.message);
});

consumer.on('processing_error', (err) => {
    console.error('Processing error:', err.message);
});

// Routes
app.get('/health', (req, res) => {
    res.status(200).json({
        status: 'healthy',
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        sqs_status: consumer.isRunning ? 'running' : 'stopped',
        queue_url: SQS_QUEUE_URL,
        environment: process.env.ad_env || 'development'
    });
});

fs.readdirSync('./form_api').forEach(form_name => {
    const route = require(`./form_api/${form_name}`);
    for (const function_name in route) {
        app.get(`/api/${form_name.replace('.js', '')}/${function_name}`, route[function_name]);
        app.post(`/api/${form_name.replace('.js', '')}/${function_name}`, route[function_name]);
    }
});

app._router.stack
    .filter(r => r.route)
    .map(r => {
        console.log(`${Object.keys(r.route.methods)} ${r.route.path}`);
    });


// SQS status endpoints
app.post('/sqs/start', (req, res) => {
    if (!consumer.isRunning) {
        consumer.start();
        res.status(200).json({ message: 'SQS consumer started' });
    } else {
        res.status(400).json({ message: 'SQS consumer is already running' });
    }
});

app.post('/sqs/stop', (req, res) => {
    if (consumer.isRunning) {
        consumer.stop();
        res.status(200).json({ message: 'SQS consumer stopped' });
    } else {
        res.status(400).json({ message: 'SQS consumer is already stopped' });
    }
});

app.use((err, req, res, next) => {
    console.error(err.stack);
    res.status(500).json({
        status: 'error',
        message: 'Internal server error',
        error: process.env.NODE_ENV === 'development' ? err.message : undefined
    });
});

// Start server
const PORT = process.env.PORT || 3000;
const server = app.listen(PORT, () => {
    const files = fs.readdirSync('.');
    files.filter(f => f.startsWith('Recorder_'))
        .forEach(f => fs.unlinkSync(f));

    console.log(`Express server is running on port ${PORT}`);
    console.log(`SQS Consumer is listening on: ${SQS_QUEUE_URL}`);
    consumer.start();
});

// Graceful shutdown
const shutdown = async () => {
    console.log('Shutting down server...');

    // Stop SQS consumer
    if (consumer.isRunning) {
        console.log('Stopping SQS consumer...');
        consumer.stop();
    }

    // Close Express server
    server.close(() => {
        console.log('Express server closed');
        process.exit(0);
    });

    // Force exit if graceful shutdown fails
    setTimeout(() => {
        console.error('Could not close connections in time, forcefully shutting down');
        process.exit(1);
    }, 10000);
};

process.on('SIGTERM', shutdown);
process.on('SIGINT', shutdown);