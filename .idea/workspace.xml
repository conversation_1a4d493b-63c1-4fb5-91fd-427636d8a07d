<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="ChangeListManager">
    <list default="true" id="f63f4523-037c-496d-82a2-ceed719be232" name="Default Changelist" comment="">
      <change beforePath="$PROJECT_DIR$/Makefile" beforeDir="false" afterPath="$PROJECT_DIR$/Makefile" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/go.mod" beforeDir="false" afterPath="$PROJECT_DIR$/go.mod" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/go.sum" beforeDir="false" afterPath="$PROJECT_DIR$/go.sum" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/handlers/packages/v1/package_handlers.go" beforeDir="false" afterPath="$PROJECT_DIR$/handlers/packages/v1/package_handlers.go" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/sdk/db/email_template_dao.go" beforeDir="false" afterPath="$PROJECT_DIR$/sdk/db/email_template_dao.go" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/user-authentication/main.go" beforeDir="false" afterPath="$PROJECT_DIR$/user-authentication/main.go" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/user-authentication/utils/db.go" beforeDir="false" afterPath="$PROJECT_DIR$/user-authentication/utils/db.go" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="GOROOT" path="/usr/local/go" />
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="GoLibraries">
    <option name="indexEntireGoPath" value="false" />
  </component>
  <component name="ProjectId" id="1cWSjoFvv2RwYOBlYj5kIT3UPaj" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">
    <property name="RunOnceActivity.OpenProjectViewOnStart" value="true" />
    <property name="RunOnceActivity.ShowReadmeOnStart" value="true" />
    <property name="WebServerToolWindowFactoryState" value="false" />
    <property name="go.import.settings.migrated" value="true" />
    <property name="go.sdk.automatically.set" value="true" />
    <property name="go.tried.to.enable.integration.vgo.integrator" value="true" />
    <property name="last_opened_file_path" value="$PROJECT_DIR$" />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="2" />
  </component>
  <component name="VgoProject">
    <integration-enabled>true</integration-enabled>
  </component>
</project>